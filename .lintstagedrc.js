export default {
  // TypeScript check disabled in pre-commit hook due to project-wide type errors
  // "**/*.ts?(x)": (filenames) => `npx tsc --noEmit --skipLibCheck ${filenames.join(" ")}`,
  // Lint & prettify TS and JS files
  "**/*.(ts|tsx|js|jsx)": (filenames) => [
    `npx eslint ${filenames.join(" ")}`,
    `npx prettier --write ${filenames.join(" ")}`,
  ],
  // Prettify only Markdown and JSON files
  "**/*.(md|json)": (filenames) => `npx prettier --write ${filenames.join(" ")}`,
}
