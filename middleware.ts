import { NextRequest, NextResponse } from "next/server"

// Define protected API routes
const protectedApiRoutes = [
  "/api/ai",
  "/api/weather",
  "/api/places",
  "/api/images/google-places",
  "/api/email",
]

const bypassRoutes = ["/api/places/autocomplete-public"]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if the request is for a protected API route
  const isProtectedApiRoute = protectedApiRoutes.some((route) => pathname.startsWith(route))
  const isBypassRoute = bypassRoutes.some((route) => pathname.startsWith(route))

  if (isProtectedApiRoute && !isBypassRoute) {
    try {
      // Get the Authorization header
      const authHeader = request.headers.get("Authorization")

      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return NextResponse.json(
          { error: "Unauthorized: Missing or invalid token" },
          { status: 401 }
        )
      }

      // Extract the token and pass it to the API route
      const token = authHeader.split("Bearer ")[1]

      // Add the token to the request headers for the API route to verify
      const requestHeaders = new Headers(request.headers)
      requestHeaders.set("X-Auth-Token", token)

      // Continue to the API route with the token in headers
      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      })
    } catch (error) {
      console.error("Error in middleware:", error)
      return NextResponse.json({ error: "Authentication error" }, { status: 500 })
    }
  }

  // For non-protected routes, continue normally
  return NextResponse.next()
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    "/api/ai/:path*",
    "/api/weather/:path*",
    "/api/places/:path*",
    "/api/images/google-places/:path*",
    "/api/email/:path*",
  ],
}
