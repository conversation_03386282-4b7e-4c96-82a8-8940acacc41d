"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { ArrowLeft, Calendar, Lock, Users, CreditCard, Wallet, BellRing } from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export default function CreateSavingsGoalPage() {
  const [goalName, setGoalName] = useState("")
  const [goalAmount, setGoalAmount] = useState(1000)
  const [targetDate, setTargetDate] = useState("")
  const [selectedTrip, setSelectedTrip] = useState("")
  const [depositFrequency, setDepositFrequency] = useState("weekly")
  const [depositMethod, setDepositMethod] = useState("manual")
  const [isPrivate, setIsPrivate] = useState(false)
  const [enableReminders, setEnableReminders] = useState(true)
  const [depositAmount, setDepositAmount] = useState(50)

  // Calculate number of deposits and end date
  const today = new Date()
  const endDate = targetDate ? new Date(targetDate) : new Date(today.setMonth(today.getMonth() + 3))
  const daysDiff = Math.ceil((endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))

  let numberOfDeposits = 0
  if (depositFrequency === "weekly") {
    numberOfDeposits = Math.ceil(daysDiff / 7)
  } else if (depositFrequency === "biweekly") {
    numberOfDeposits = Math.ceil(daysDiff / 14)
  } else if (depositFrequency === "monthly") {
    numberOfDeposits = Math.ceil(daysDiff / 30)
  }

  // Calculate suggested deposit amount
  const suggestedDepositAmount =
    numberOfDeposits > 0 ? Math.ceil(goalAmount / numberOfDeposits) : goalAmount

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6 flex items-center">
            <Link href="/savings" className="mr-4">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold">Create Savings Goal</h1>
              <p className="text-muted-foreground">
                Set up a new savings goal for your upcoming trip
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Goal Details</CardTitle>
                  <CardDescription>Set your savings target and timeline</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="goal-name">Goal Name</Label>
                    <Input
                      id="goal-name"
                      placeholder="e.g., Lake Tahoe Trip Fund"
                      value={goalName}
                      onChange={(e) => setGoalName(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="trip">Link to Trip (Optional)</Label>
                    <Select value={selectedTrip} onValueChange={setSelectedTrip}>
                      <SelectTrigger id="trip">
                        <SelectValue placeholder="Select a trip" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No linked trip</SelectItem>
                        <SelectItem value="1">Lake Tahoe (Aug 15-20, 2023)</SelectItem>
                        <SelectItem value="2">Yosemite National Park (Sep 5-10, 2023)</SelectItem>
                        <SelectItem value="7">San Diego Beach Weekend (Jul 8-10, 2023)</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">
                      Linking to a trip will help track your squad's overall savings progress
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="goal-amount">Goal Amount ($)</Label>
                      <Input
                        id="goal-amount"
                        type="number"
                        min="1"
                        value={goalAmount}
                        onChange={(e) => setGoalAmount(Number(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="target-date">Target Date</Label>
                      <Input
                        id="target-date"
                        type="date"
                        value={targetDate}
                        onChange={(e) => setTargetDate(e.target.value)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Saving Plan</CardTitle>
                  <CardDescription>Configure how you'll reach your goal</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Deposit Frequency</Label>
                    <RadioGroup
                      value={depositFrequency}
                      onValueChange={setDepositFrequency}
                      className="flex flex-col space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="weekly" id="weekly" />
                        <Label htmlFor="weekly">Weekly</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="biweekly" id="biweekly" />
                        <Label htmlFor="biweekly">Bi-weekly</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="monthly" id="monthly" />
                        <Label htmlFor="monthly">Monthly</Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <div className="space-y-2">
                    <Label>Deposit Method</Label>
                    <RadioGroup
                      value={depositMethod}
                      onValueChange={setDepositMethod}
                      className="flex flex-col space-y-1"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="manual" id="manual" />
                        <Label htmlFor="manual" className="flex items-center gap-2">
                          <Wallet className="h-4 w-4" /> Manual Deposits
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="automatic" id="automatic" />
                        <Label htmlFor="automatic" className="flex items-center gap-2">
                          <CreditCard className="h-4 w-4" /> Automatic Deposits
                        </Label>
                      </div>
                    </RadioGroup>
                    <p className="text-xs text-muted-foreground">
                      Automatic deposits will be scheduled according to your selected frequency
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>Deposit Amount (${depositAmount})</Label>
                    <Slider
                      value={[depositAmount]}
                      min={10}
                      max={Math.max(200, suggestedDepositAmount * 2)}
                      step={5}
                      onValueChange={(value) => setDepositAmount(value[0])}
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>$10</span>
                      <span>Suggested: ${suggestedDepositAmount}</span>
                      <span>${Math.max(200, suggestedDepositAmount * 2)}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2">
                    <div className="space-y-0.5">
                      <Label htmlFor="reminders">Savings Reminders</Label>
                      <p className="text-sm text-muted-foreground">
                        Get notifications when it's time to save
                      </p>
                    </div>
                    <Switch
                      id="reminders"
                      checked={enableReminders}
                      onCheckedChange={setEnableReminders}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Privacy Settings</CardTitle>
                  <CardDescription>Control who can see your savings progress</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="private">Private Goal</Label>
                      <p className="text-sm text-muted-foreground">
                        {isPrivate
                          ? "Only you can see your savings progress"
                          : "Your squad can see your savings progress"}
                      </p>
                    </div>
                    <Switch id="private" checked={isPrivate} onCheckedChange={setIsPrivate} />
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Goal Summary</CardTitle>
                  <CardDescription>Review your savings plan</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">Goal Details</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Goal Amount:</span>
                        <span className="font-medium">${goalAmount.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Target Date:</span>
                        <span className="font-medium">
                          {targetDate ? new Date(targetDate).toLocaleDateString() : "Not set"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Days Remaining:</span>
                        <span className="font-medium">{daysDiff} days</span>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <h3 className="font-medium mb-2">Deposit Plan</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Frequency:</span>
                        <span className="font-medium capitalize">{depositFrequency}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Method:</span>
                        <span className="font-medium capitalize">{depositMethod}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Amount per Deposit:</span>
                        <span className="font-medium">${depositAmount}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Number of Deposits:</span>
                        <span className="font-medium">{numberOfDeposits}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Total to be Saved:</span>
                        <span className="font-medium">
                          ${(depositAmount * numberOfDeposits).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <h3 className="font-medium mb-2">Settings</h3>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        {isPrivate ? (
                          <Lock className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Users className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span className="text-muted-foreground">
                          {isPrivate ? "Private goal" : "Visible to squad"}
                        </span>
                      </div>
                      {enableReminders && (
                        <div className="flex items-center gap-2">
                          <BellRing className="h-4 w-4 text-muted-foreground" />
                          <span className="text-muted-foreground">Reminders enabled</span>
                        </div>
                      )}
                      {selectedTrip && (
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-muted-foreground">
                            Linked to trip: {trips.find((t) => t.id === selectedTrip)?.name}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col gap-2">
                  <Button className="w-full" disabled={!goalName || goalAmount <= 0}>
                    Create Savings Goal
                  </Button>
                  <Link href="/savings" className="w-full">
                    <Button variant="outline" className="w-full">
                      Cancel
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

// Sample trip data
const trips = [
  { id: "1", name: "Lake Tahoe (Aug 15-20, 2023)" },
  { id: "2", name: "Yosemite National Park (Sep 5-10, 2023)" },
  { id: "7", name: "San Diego Beach Weekend (Jul 8-10, 2023)" },
]
