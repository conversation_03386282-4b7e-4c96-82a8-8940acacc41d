"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import {
  ArrowLeft,
  DollarSign,
  Calendar,
  TrendingUp,
  Lock,
  Users,
  Edit,
  Trash2,
  Plus,
  CreditCard,
  Wallet,
  Share2,
} from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export default function SavingsGoalDetailPage({ params }: { params: { id: string } }) {
  // Find the goal based on the ID
  const goal = savingsGoals.find((g) => g.id === params.id) || savingsGoals[0]

  const [activeTab, setActiveTab] = useState("overview")
  const [isPrivate, setIsPrivate] = useState(goal.isPrivate)
  const [enableReminders, setEnableReminders] = useState(true)
  const [newDepositAmount, setNewDepositAmount] = useState("")
  const [depositDate, setDepositDate] = useState(new Date().toISOString().split("T")[0])
  const [depositMethod, setDepositMethod] = useState("manual")

  // Calculate progress percentage
  const progressPercentage = Math.round((goal.savedAmount / goal.goalAmount) * 100)

  // Calculate daily savings rate needed
  const dailySavingsNeeded =
    goal.daysLeft > 0 ? Math.ceil((goal.goalAmount - goal.savedAmount) / goal.daysLeft) : 0

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6 flex items-center">
            <Link href="/savings" className="mr-4">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h1 className="text-3xl font-bold">{goal.name}</h1>
                <Badge
                  className={
                    goal.status === "on-track"
                      ? "bg-green-100 text-green-800"
                      : goal.status === "behind"
                        ? "bg-yellow-100 text-yellow-800"
                        : goal.status === "completed"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                  }
                >
                  {goal.status === "on-track"
                    ? "On Track"
                    : goal.status === "behind"
                      ? "Behind"
                      : goal.status === "completed"
                        ? "Completed"
                        : "Draft"}
                </Badge>
              </div>
              <div className="flex items-center gap-4 mt-1">
                <div className="flex items-center gap-1 text-muted-foreground">
                  <DollarSign className="h-4 w-4" />
                  <span>${goal.goalAmount.toLocaleString()} goal</span>
                </div>
                {goal.tripName && (
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>For {goal.tripName}</span>
                  </div>
                )}
                <div className="flex items-center gap-1 text-muted-foreground">
                  {goal.isPrivate ? <Lock className="h-4 w-4" /> : <Users className="h-4 w-4" />}
                  <span>{goal.isPrivate ? "Private" : "Visible to squad"}</span>
                </div>
              </div>
            </div>
          </div>

          <Tabs defaultValue="overview" className="space-y-4" onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="transactions">Transactions</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="md:col-span-2">
                  <CardHeader className="pb-2">
                    <CardTitle>Savings Progress</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm text-muted-foreground">Saved</p>
                        <p className="text-3xl font-bold">${goal.savedAmount.toLocaleString()}</p>
                      </div>
                      <div className="text-center">
                        <div className="h-24 w-24 rounded-full border-8 border-primary/20 flex items-center justify-center relative">
                          <div
                            className="absolute inset-0 rounded-full border-8 border-primary"
                            style={{
                              clipPath: `polygon(0 0, 100% 0, 100% 100%, 0% 100%)`,
                              opacity: progressPercentage / 100,
                            }}
                          ></div>
                          <span className="text-2xl font-bold">{progressPercentage}%</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-muted-foreground">Goal</p>
                        <p className="text-3xl font-bold">${goal.goalAmount.toLocaleString()}</p>
                      </div>
                    </div>

                    <Progress value={progressPercentage} className="h-2" />

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2">
                      <div className="p-3 rounded-md bg-muted/50 space-y-1">
                        <p className="text-sm text-muted-foreground">Remaining</p>
                        <p className="text-xl font-bold">
                          ${(goal.goalAmount - goal.savedAmount).toLocaleString()}
                        </p>
                      </div>
                      <div className="p-3 rounded-md bg-muted/50 space-y-1">
                        <p className="text-sm text-muted-foreground">Days Left</p>
                        <p className="text-xl font-bold">{goal.daysLeft}</p>
                      </div>
                      <div className="p-3 rounded-md bg-muted/50 space-y-1">
                        <p className="text-sm text-muted-foreground">Daily Rate</p>
                        <p className="text-xl font-bold">${dailySavingsNeeded}</p>
                      </div>
                      <div className="p-3 rounded-md bg-muted/50 space-y-1">
                        <p className="text-sm text-muted-foreground">Weekly Rate</p>
                        <p className="text-xl font-bold">
                          ${(dailySavingsNeeded * 7).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button className="w-full">
                          <Plus className="mr-2 h-4 w-4" /> Add Deposit
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Add Deposit</DialogTitle>
                          <DialogDescription>
                            Record a new deposit towards your savings goal.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                          <div className="space-y-2">
                            <Label htmlFor="deposit-amount">Amount ($)</Label>
                            <Input
                              id="deposit-amount"
                              placeholder="Enter amount"
                              type="number"
                              value={newDepositAmount}
                              onChange={(e) => setNewDepositAmount(e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="deposit-date">Date</Label>
                            <Input
                              id="deposit-date"
                              type="date"
                              value={depositDate}
                              onChange={(e) => setDepositDate(e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="deposit-method">Payment Method</Label>
                            <Select value={depositMethod} onValueChange={setDepositMethod}>
                              <SelectTrigger id="deposit-method">
                                <SelectValue placeholder="Select method" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="manual">Manual Record</SelectItem>
                                <SelectItem value="bank">Bank Transfer</SelectItem>
                                <SelectItem value="card">Credit/Debit Card</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <DialogFooter>
                          <Button type="submit">Save Deposit</Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </CardFooter>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Upcoming</CardTitle>
                    <CardDescription>Your savings schedule</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {goal.isAutomatic ? (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 rounded-md bg-muted/50">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-primary" />
                            <div>
                              <p className="font-medium">Next Deposit</p>
                              <p className="text-xs text-muted-foreground">July 15, 2023</p>
                            </div>
                          </div>
                          <Badge variant="outline">${goal.weeklyAmount}</Badge>
                        </div>

                        <div className="flex items-center justify-between p-3 rounded-md bg-muted/50">
                          <div className="flex items-center gap-2">
                            <TrendingUp className="h-4 w-4 text-primary" />
                            <div>
                              <p className="font-medium">75% Milestone</p>
                              <p className="text-xs text-muted-foreground">Est. July 29, 2023</p>
                            </div>
                          </div>
                          <Badge>${(goal.goalAmount * 0.75).toLocaleString()}</Badge>
                        </div>

                        <div className="flex items-center justify-between p-3 rounded-md bg-muted/50">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-primary" />
                            <div>
                              <p className="font-medium">Goal Completion</p>
                              <p className="text-xs text-muted-foreground">Est. August 26, 2023</p>
                            </div>
                          </div>
                          <Badge>${goal.goalAmount.toLocaleString()}</Badge>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 rounded-md bg-muted/50">
                          <div className="flex items-center gap-2">
                            <TrendingUp className="h-4 w-4 text-primary" />
                            <div>
                              <p className="font-medium">Suggested Weekly</p>
                              <p className="text-xs text-muted-foreground">To reach goal on time</p>
                            </div>
                          </div>
                          <Badge variant="outline">
                            ${(dailySavingsNeeded * 7).toLocaleString()}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between p-3 rounded-md bg-muted/50">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-primary" />
                            <div>
                              <p className="font-medium">Goal Deadline</p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(
                                  Date.now() + goal.daysLeft * 24 * 60 * 60 * 1000
                                ).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                          <Badge>${goal.goalAmount.toLocaleString()}</Badge>
                        </div>

                        <div className="p-3 rounded-md border border-dashed flex flex-col items-center justify-center text-center">
                          <p className="text-sm text-muted-foreground mb-2">
                            Set up automatic deposits to stay on track
                          </p>
                          <Button variant="outline" size="sm">
                            Set Up Auto-Deposits
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Your latest deposits and milestones</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {transactions.slice(0, 5).map((transaction, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 rounded-md border"
                      >
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                            {transaction.type === "deposit" ? (
                              <DollarSign className="h-5 w-5 text-primary" />
                            ) : (
                              <TrendingUp className="h-5 w-5 text-primary" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{transaction.description}</p>
                            <p className="text-xs text-muted-foreground">{transaction.date}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          {transaction.type === "deposit" && (
                            <p className="font-medium text-green-600">
                              +${transaction.amount.toLocaleString()}
                            </p>
                          )}
                          {transaction.type === "milestone" && (
                            <Badge>{transaction.milestone}% Complete</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => setActiveTab("transactions")}
                  >
                    View All Transactions
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="transactions" className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>All Transactions</CardTitle>
                    <CardDescription>Your complete savings history</CardDescription>
                  </div>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button>
                        <Plus className="mr-2 h-4 w-4" /> Add Deposit
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add Deposit</DialogTitle>
                        <DialogDescription>
                          Record a new deposit towards your savings goal.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="deposit-amount-2">Amount ($)</Label>
                          <Input
                            id="deposit-amount-2"
                            placeholder="Enter amount"
                            type="number"
                            value={newDepositAmount}
                            onChange={(e) => setNewDepositAmount(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="deposit-date-2">Date</Label>
                          <Input
                            id="deposit-date-2"
                            type="date"
                            value={depositDate}
                            onChange={(e) => setDepositDate(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="deposit-method-2">Payment Method</Label>
                          <Select value={depositMethod} onValueChange={setDepositMethod}>
                            <SelectTrigger id="deposit-method-2">
                              <SelectValue placeholder="Select method" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="manual">Manual Record</SelectItem>
                              <SelectItem value="bank">Bank Transfer</SelectItem>
                              <SelectItem value="card">Credit/Debit Card</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <DialogFooter>
                        <Button type="submit">Save Deposit</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {transactions.map((transaction, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 rounded-md border"
                      >
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                            {transaction.type === "deposit" ? (
                              transaction.method === "card" ? (
                                <CreditCard className="h-5 w-5 text-primary" />
                              ) : transaction.method === "bank" ? (
                                <Wallet className="h-5 w-5 text-primary" />
                              ) : (
                                <DollarSign className="h-5 w-5 text-primary" />
                              )
                            ) : (
                              <TrendingUp className="h-5 w-5 text-primary" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{transaction.description}</p>
                            <p className="text-xs text-muted-foreground">
                              {transaction.date}
                              {transaction.type === "deposit" && transaction.method && (
                                <span>
                                  {" "}
                                  •{" "}
                                  {transaction.method === "card"
                                    ? "Credit Card"
                                    : transaction.method === "bank"
                                      ? "Bank Transfer"
                                      : "Manual"}
                                </span>
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {transaction.type === "deposit" && (
                            <p className="font-medium text-green-600">
                              +${transaction.amount.toLocaleString()}
                            </p>
                          )}
                          {transaction.type === "milestone" && (
                            <Badge>{transaction.milestone}% Complete</Badge>
                          )}
                          {transaction.type === "deposit" && (
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <Trash2 className="h-4 w-4 text-muted-foreground" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Goal Settings</CardTitle>
                  <CardDescription>Manage your savings goal preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="private-setting">Private Goal</Label>
                      <p className="text-sm text-muted-foreground">
                        {isPrivate
                          ? "Only you can see your savings progress"
                          : "Your squad can see your savings progress"}
                      </p>
                    </div>
                    <Switch
                      id="private-setting"
                      checked={isPrivate}
                      onCheckedChange={setIsPrivate}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="reminders-setting">Savings Reminders</Label>
                      <p className="text-sm text-muted-foreground">
                        {enableReminders
                          ? "Get notifications when it's time to save"
                          : "No reminders for this goal"}
                      </p>
                    </div>
                    <Switch
                      id="reminders-setting"
                      checked={enableReminders}
                      onCheckedChange={setEnableReminders}
                    />
                  </div>

                  <div className="pt-4 border-t">
                    <h3 className="font-medium mb-2">Goal Details</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">Goal Name:</span>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{goal.name}</span>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">Goal Amount:</span>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">${goal.goalAmount.toLocaleString()}</span>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">Target Date:</span>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {new Date(
                              Date.now() + goal.daysLeft * 24 * 60 * 60 * 1000
                            ).toLocaleDateString()}
                          </span>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <h3 className="font-medium mb-2">Deposit Settings</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-muted-foreground">Deposit Method:</span>
                        <div className="flex items-center gap-2">
                          <span className="font-medium capitalize">
                            {goal.isAutomatic ? "Automatic" : "Manual"}
                          </span>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <Edit className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </div>
                      </div>
                      {goal.isAutomatic && (
                        <div className="flex justify-between items-center">
                          <span className="text-muted-foreground">Weekly Amount:</span>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">${goal.weeklyAmount}</span>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <Edit className="h-4 w-4 text-muted-foreground" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex flex-col gap-2">
                  <Button variant="outline" className="w-full">
                    <Share2 className="mr-2 h-4 w-4" /> Share Goal
                  </Button>
                  <Button variant="destructive" className="w-full">
                    <Trash2 className="mr-2 h-4 w-4" /> Delete Goal
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Notification Preferences</CardTitle>
                  <CardDescription>Manage how you receive savings reminders</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="email-notifications">Email Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive savings reminders via email
                      </p>
                    </div>
                    <Switch id="email-notifications" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="push-notifications">Push Notifications</Label>
                      <p className="text-sm text-muted-foreground">
                        Receive savings reminders on your device
                      </p>
                    </div>
                    <Switch id="push-notifications" defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="milestone-notifications">Milestone Celebrations</Label>
                      <p className="text-sm text-muted-foreground">
                        Get notified when you reach savings milestones
                      </p>
                    </div>
                    <Switch id="milestone-notifications" defaultChecked />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}

// Sample data for the savings goal
const savingsGoals = [
  {
    id: "1",
    name: "Lake Tahoe Trip",
    tripName: "Lake Tahoe",
    savedAmount: 750,
    goalAmount: 1200,
    daysLeft: 45,
    status: "on-track",
    isPrivate: false,
    isAutomatic: true,
    weeklyAmount: 50,
  },
  {
    id: "2",
    name: "Yosemite Adventure",
    tripName: "Yosemite National Park",
    savedAmount: 400,
    goalAmount: 1500,
    daysLeft: 90,
    status: "behind",
    isPrivate: false,
    isAutomatic: false,
  },
  {
    id: "3",
    name: "New Orleans Trip",
    tripName: "New Orleans",
    savedAmount: 600,
    goalAmount: 800,
    daysLeft: 30,
    status: "on-track",
    isPrivate: true,
    isAutomatic: true,
    weeklyAmount: 40,
  },
]

// Sample transactions
const transactions = [
  {
    type: "deposit",
    description: "Weekly Deposit",
    amount: 50,
    date: "July 1, 2023",
    method: "card",
  },
  {
    type: "deposit",
    description: "Weekly Deposit",
    amount: 50,
    date: "June 24, 2023",
    method: "card",
  },
  {
    type: "milestone",
    description: "50% Milestone Reached",
    date: "June 24, 2023",
    milestone: 50,
  },
  {
    type: "deposit",
    description: "Extra Deposit",
    amount: 100,
    date: "June 20, 2023",
    method: "bank",
  },
  {
    type: "deposit",
    description: "Weekly Deposit",
    amount: 50,
    date: "June 17, 2023",
    method: "card",
  },
  {
    type: "deposit",
    description: "Weekly Deposit",
    amount: 50,
    date: "June 10, 2023",
    method: "card",
  },
  {
    type: "deposit",
    description: "Weekly Deposit",
    amount: 50,
    date: "June 3, 2023",
    method: "card",
  },
  {
    type: "milestone",
    description: "25% Milestone Reached",
    date: "June 3, 2023",
    milestone: 25,
  },
  {
    type: "deposit",
    description: "Initial Deposit",
    amount: 400,
    date: "May 27, 2023",
    method: "bank",
  },
]
