"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import {
  Plus,
  Search,
  Calendar,
  DollarSign,
  TrendingUp,
  Lock,
  Users,
  ChevronRight,
  CheckCircle2,
} from "lucide-react"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export default function SavingsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")

  // Filter savings goals based on search query and status filter
  const filteredActiveGoals = savingsGoals.filter(
    (goal) =>
      goal.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      (filterStatus === "all" || goal.status === filterStatus) &&
      goal.status !== "completed"
  )

  const filteredCompletedGoals = savingsGoals.filter(
    (goal) =>
      goal.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
      (filterStatus === "all" || goal.status === "completed") &&
      goal.status === "completed"
  )

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">Trip Savings</h1>
              <p className="text-muted-foreground">
                Track and manage your savings goals for upcoming trips
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search savings goals..."
                  className="pl-10 w-full sm:w-[200px] md:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="on-track">On Track</SelectItem>
                  <SelectItem value="behind">Behind</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>

              <Link href="/savings/create">
                <Button>
                  <Plus className="mr-2 h-4 w-4" /> New Goal
                </Button>
              </Link>
            </div>
          </div>

          <Tabs defaultValue="active" className="space-y-4">
            <TabsList>
              <TabsTrigger value="active">Active Goals</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="insights">Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="active" className="space-y-4">
              {filteredActiveGoals.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredActiveGoals.map((goal) => (
                    <SavingsGoalCard key={goal.id} goal={goal} />
                  ))}

                  <Card className="border-dashed hover:border-primary/50 cursor-pointer transition-colors">
                    <CardContent className="flex flex-col items-center justify-center h-full p-6">
                      <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                        <Plus className="h-6 w-6 text-primary" />
                      </div>
                      <p className="font-medium text-center">Create a New Savings Goal</p>
                      <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
                        Start saving for your next adventure
                      </p>
                      <Link href="/savings/create">
                        <Button variant="outline">Get Started</Button>
                      </Link>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-12 border rounded-lg">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                    <DollarSign className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">No savings goals found</h3>
                  <p className="text-muted-foreground text-center mb-6">
                    {searchQuery || filterStatus !== "all"
                      ? "We couldn't find any savings goals matching your search criteria."
                      : "You haven't created any savings goals yet. Start saving for your next trip!"}
                  </p>
                  {searchQuery || filterStatus !== "all" ? (
                    <Button
                      onClick={() => {
                        setSearchQuery("")
                        setFilterStatus("all")
                      }}
                    >
                      Clear Filters
                    </Button>
                  ) : (
                    <Link href="/savings/create">
                      <Button>
                        <Plus className="mr-2 h-4 w-4" /> Create Your First Goal
                      </Button>
                    </Link>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="completed" className="space-y-4">
              {filteredCompletedGoals.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredCompletedGoals.map((goal) => (
                    <SavingsGoalCard key={goal.id} goal={goal} />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center p-12 border rounded-lg">
                  <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                    <CheckCircle2 className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">No completed goals yet</h3>
                  <p className="text-muted-foreground text-center mb-6">
                    Keep saving! Your completed goals will appear here.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Savings Overview</CardTitle>
                    <CardDescription>Your overall savings progress</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <h3 className="font-medium">Total Saved</h3>
                        <span className="font-bold">${totalSaved.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <h3 className="font-medium">Total Goals</h3>
                        <span className="font-bold">${totalGoals.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <h3 className="font-medium">Overall Progress</h3>
                        <span className="font-bold">
                          {Math.round((totalSaved / totalGoals) * 100)}%
                        </span>
                      </div>
                      <Progress value={(totalSaved / totalGoals) * 100} className="h-2" />
                    </div>

                    <div className="pt-4 border-t">
                      <h3 className="font-medium mb-2">Upcoming Milestones</h3>
                      {upcomingMilestones.length > 0 ? (
                        <div className="space-y-2">
                          {upcomingMilestones.map((milestone, index) => (
                            <div
                              key={index}
                              className="flex justify-between items-center p-2 rounded-md bg-muted/50"
                            >
                              <div className="flex items-center gap-2">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                  {milestone.icon}
                                </div>
                                <div>
                                  <p className="font-medium">{milestone.name}</p>
                                  <p className="text-xs text-muted-foreground">{milestone.date}</p>
                                </div>
                              </div>
                              <Badge variant={milestone.type === "deposit" ? "outline" : "default"}>
                                {milestone.type === "deposit" ? "Deposit Due" : "Milestone"}
                              </Badge>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">No upcoming milestones</p>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Saving Habits</CardTitle>
                    <CardDescription>Your saving patterns and trends</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <h3 className="font-medium">Monthly Savings</h3>
                      <div className="h-40 flex items-end gap-2">
                        {monthlySavings.map((month, index) => (
                          <div key={index} className="flex-1 flex flex-col items-center gap-1">
                            <div
                              className="w-full bg-primary/20 rounded-t-sm"
                              style={{ height: `${(month.amount / maxMonthlySaving) * 100}%` }}
                            ></div>
                            <span className="text-xs text-muted-foreground">{month.name}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="pt-4 border-t">
                      <h3 className="font-medium mb-2">Saving Streaks</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <p className="text-sm">Current streak</p>
                          <Badge variant="outline">{currentStreak} weeks</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <p className="text-sm">Longest streak</p>
                          <Badge variant="outline">{longestStreak} weeks</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <p className="text-sm">Consistency rate</p>
                          <Badge variant="outline">{consistencyRate}%</Badge>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Squad Savings</CardTitle>
                  <CardDescription>
                    How your squads are progressing towards trip goals
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {squadSavings.map((squad) => (
                      <div key={squad.id} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <h3 className="font-medium">{squad.name}</h3>
                          <Link href={`/squads/${squad.id}`}>
                            <Button variant="ghost" size="sm" className="h-8 gap-1">
                              View Squad <ChevronRight className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                        <div className="space-y-3">
                          {squad.trips.map((trip, tripIndex) => (
                            <div key={tripIndex} className="rounded-md border p-3">
                              <div className="flex justify-between items-center mb-2">
                                <div className="flex items-center gap-2">
                                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                    <DollarSign className="h-4 w-4 text-primary" />
                                  </div>
                                  <div>
                                    <p className="font-medium">{trip.destination}</p>
                                    <p className="text-xs text-muted-foreground">{trip.date}</p>
                                  </div>
                                </div>
                                <Badge
                                  variant={
                                    trip.readiness >= 80
                                      ? "default"
                                      : trip.readiness >= 50
                                        ? "secondary"
                                        : "outline"
                                  }
                                >
                                  {trip.readiness}% Ready
                                </Badge>
                              </div>
                              <Progress value={trip.readiness} className="h-2 mb-2" />
                              <div className="flex items-center justify-between text-xs text-muted-foreground">
                                <span>
                                  {trip.membersReady} of {trip.totalMembers} members ready
                                </span>
                                <span>
                                  ${trip.savedAmount.toLocaleString()} of $
                                  {trip.totalAmount.toLocaleString()}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}

function SavingsGoalCard({ goal }: { goal: any }) {
  return (
    <Link href={`/savings/${goal.id}`}>
      <Card className="h-full hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{goal.name}</CardTitle>
              <CardDescription>
                {goal.tripName ? `For ${goal.tripName}` : "Personal Goal"}
              </CardDescription>
            </div>
            <Badge
              className={
                goal.status === "on-track"
                  ? "bg-green-100 text-green-800"
                  : goal.status === "behind"
                    ? "bg-yellow-100 text-yellow-800"
                    : goal.status === "completed"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-gray-100 text-gray-800"
              }
            >
              {goal.status === "on-track"
                ? "On Track"
                : goal.status === "behind"
                  ? "Behind"
                  : goal.status === "completed"
                    ? "Completed"
                    : "Draft"}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-muted-foreground">Saved</p>
              <p className="text-xl font-bold">${goal.savedAmount.toLocaleString()}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Goal</p>
              <p className="text-xl font-bold">${goal.goalAmount.toLocaleString()}</p>
            </div>
          </div>

          <div className="space-y-1">
            <div className="flex justify-between text-sm">
              <span>{Math.round((goal.savedAmount / goal.goalAmount) * 100)}%</span>
              <span>{goal.daysLeft} days left</span>
            </div>
            <Progress value={(goal.savedAmount / goal.goalAmount) * 100} className="h-2" />
          </div>

          <div className="flex items-center gap-2 text-sm">
            {goal.isPrivate ? (
              <div className="flex items-center gap-1 text-muted-foreground">
                <Lock className="h-3 w-3" />
                <span>Private</span>
              </div>
            ) : (
              <div className="flex items-center gap-1 text-muted-foreground">
                <Users className="h-3 w-3" />
                <span>Visible to squad</span>
              </div>
            )}

            {goal.isAutomatic && (
              <div className="flex items-center gap-1 text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>Auto-deposit</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}

// Sample data
const savingsGoals = [
  {
    id: "1",
    name: "Lake Tahoe Trip",
    tripName: "Lake Tahoe",
    savedAmount: 750,
    goalAmount: 1200,
    daysLeft: 45,
    status: "on-track",
    isPrivate: false,
    isAutomatic: true,
  },
  {
    id: "2",
    name: "Yosemite Adventure",
    tripName: "Yosemite National Park",
    savedAmount: 400,
    goalAmount: 1500,
    daysLeft: 90,
    status: "behind",
    isPrivate: false,
    isAutomatic: false,
  },
  {
    id: "3",
    name: "New Orleans Trip",
    tripName: "New Orleans",
    savedAmount: 600,
    goalAmount: 800,
    daysLeft: 30,
    status: "on-track",
    isPrivate: true,
    isAutomatic: true,
  },
  {
    id: "4",
    name: "Miami Beach Vacation",
    tripName: "Miami Beach",
    savedAmount: 1200,
    goalAmount: 1200,
    daysLeft: 0,
    status: "completed",
    isPrivate: false,
    isAutomatic: true,
  },
  {
    id: "5",
    name: "New York City Weekend",
    tripName: "New York City",
    savedAmount: 950,
    goalAmount: 950,
    daysLeft: 0,
    status: "completed",
    isPrivate: true,
    isAutomatic: false,
  },
]

// Calculate totals for insights
const totalSaved = savingsGoals.reduce((sum, goal) => sum + goal.savedAmount, 0)
const totalGoals = savingsGoals.reduce((sum, goal) => sum + goal.goalAmount, 0)

// Sample upcoming milestones
const upcomingMilestones = [
  {
    name: "Lake Tahoe - 75% Milestone",
    date: "July 15, 2023",
    type: "milestone",
    icon: <TrendingUp className="h-4 w-4 text-primary" />,
  },
  {
    name: "Yosemite - Weekly Deposit",
    date: "July 18, 2023",
    type: "deposit",
    icon: <DollarSign className="h-4 w-4 text-primary" />,
  },
  {
    name: "New Orleans - 100% Goal",
    date: "July 25, 2023",
    type: "milestone",
    icon: <CheckCircle2 className="h-4 w-4 text-primary" />,
  },
]

// Sample monthly savings data
const monthlySavings = [
  { name: "Jan", amount: 200 },
  { name: "Feb", amount: 300 },
  { name: "Mar", amount: 250 },
  { name: "Apr", amount: 400 },
  { name: "May", amount: 350 },
  { name: "Jun", amount: 500 },
  { name: "Jul", amount: 450 },
]

const maxMonthlySaving = Math.max(...monthlySavings.map((month) => month.amount))

// Sample streak data
const currentStreak = 8
const longestStreak = 12
const consistencyRate = 85

// Sample squad savings data
const squadSavings = [
  {
    id: "1",
    name: "College Buddies",
    trips: [
      {
        destination: "Lake Tahoe",
        date: "Aug 15-20, 2023",
        readiness: 75,
        membersReady: 3,
        totalMembers: 5,
        savedAmount: 3750,
        totalAmount: 6000,
      },
    ],
  },
  {
    id: "3",
    name: "Hiking Crew",
    trips: [
      {
        destination: "Yosemite National Park",
        date: "Sep 5-10, 2023",
        readiness: 40,
        membersReady: 2,
        totalMembers: 4,
        savedAmount: 2400,
        totalAmount: 6000,
      },
    ],
  },
]
