"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { getUserTrips, getUserSquads, type Trip, type Squad } from "@/lib/firebase-service"
import { CalendarIcon, MapPinIcon, Users } from "lucide-react"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { PageLoading } from "@/components/page-loading"
import { OptimizedImage } from "@/components/optimized-image"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { HelpCircle } from "lucide-react"

export default function TripsPage() {
  const user = useUser()
  const router = useRouter()
  const { toast } = useToast()
  const [trips, setTrips] = useState<Trip[]>([])
  const [squads, setSquads] = useState<Squad[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        if (!user) return

        // Fetch trips and squads in parallel
        const [tripsList, squadsList] = await Promise.all([
          getUserTrips(user.uid),
          getUserSquads(user.uid),
        ])

        setTrips(tripsList || [])
        setSquads(squadsList || [])
        setLoading(false)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to load trips. Please try again.",
          variant: "destructive",
        })
        setLoading(false)
      }
    }

    fetchData()
  }, [user, router, toast])

  const formatDate = (date: Date | any) => {
    if (!date) return "TBD"
    const dateObj = date.toDate ? date.toDate() : new Date(date)
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    }).format(dateObj)
  }

  if (loading) {
    return <PageLoading message="Loading trips..." />
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />
      <div className="flex-1 flex">
        <AppSidebar />
        <main className="flex-1 p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold">My Trips</h1>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    aria-label="Squad information"
                  >
                    <HelpCircle className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-3" align="start">
                  <p className="text-sm">
                    Start planning your next trip! Trips can range from date nights to summer
                    vacations, just add the preferred Squad to your trip!
                  </p>
                </PopoverContent>
              </Popover>
            </div>
            <Button onClick={() => router.push("/trips/create")}>Plan New Trip</Button>
          </div>

          {trips.length === 0 ? (
            <div className="text-center py-12">
              <h2 className="text-2xl font-semibold mb-4">No trips yet</h2>
              <p className="text-muted-foreground mb-6">Plan your first trip with your squad</p>
              <Button onClick={() => router.push("/trips/create")}>Plan New Trip</Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {trips.map((trip) => (
                <Card
                  key={trip.id}
                  className="overflow-hidden flex flex-col h-full hover:shadow-md transition-shadow"
                >
                  <div className="aspect-video relative overflow-hidden">
                    <OptimizedImage
                      src={
                        trip.locationThumbnail ||
                        trip.image ||
                        "/placeholder.svg?height=200&width=400"
                      }
                      alt={trip.destination || trip.name}
                      aspectRatio="video"
                      className="rounded-t-lg"
                      priority
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                      <h3 className="text-white font-bold">{trip.name}</h3>
                      <p className="text-white/80 text-sm">
                        {formatDate(trip.startDate)} - {formatDate(trip.endDate)}
                      </p>
                    </div>
                  </div>
                  <CardContent className="pt-4 flex-grow">
                    <div className="flex justify-between items-center mb-2">
                      <Badge>
                        {trip.squadId
                          ? squads.find((s) => s.id === trip.squadId)?.name || "Squad Trip"
                          : "Personal Trip"}
                      </Badge>
                      {trip.attendees && (
                        <div className="text-sm text-muted-foreground flex items-center">
                          <Users className="h-3 w-3 mr-1" /> {trip.attendees.length || 0}
                        </div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <MapPinIcon className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{trip.destination || "Location TBD"}</span>
                      </div>
                      <p className="line-clamp-2 text-sm text-muted-foreground mt-2">
                        {trip.description || "No description provided"}
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="pt-0 mt-auto">
                    <Button asChild className="w-full">
                      <Link href={`/trips/${trip.id}`}>View Trip</Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
