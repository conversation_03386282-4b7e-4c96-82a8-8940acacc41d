"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, Edit, MapPin, Trash2 } from "lucide-react"
import { ItineraryItem } from "@/lib/domains/itinerary/itinerary.types"

interface ItineraryActivityItemProps {
  activity: ItineraryItem
  onEdit: (activity: ItineraryItem) => void
  onDelete: (activity: ItineraryItem) => void
}

export function ItineraryActivityItem({ activity, onEdit, onDelete }: ItineraryActivityItemProps) {
  return (
    <div className="border rounded-md p-4 space-y-2 max-w-full overflow-hidden">
      <div className="flex flex-col sm:flex-row justify-between sm:items-start gap-2">
        <div className="flex-1 max-w-full overflow-hidden">
          <h3 className="font-medium break-words">{activity.title}</h3>
          <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground mt-1">
            <Calendar className="h-3 w-3" />
            <span>
              {activity.startTime &&
                activity.startTime.toDate().toLocaleTimeString([], {
                  hour: "numeric",
                  minute: "2-digit",
                  hour12: true,
                })}
            </span>
            {activity.location && (
              <>
                <MapPin className="h-3 w-3 ml-0 sm:ml-2" />
                <span className="truncate max-w-[150px]">{activity.location}</span>
              </>
            )}
          </div>
        </div>
        <div className="flex gap-2 self-end sm:self-start">
          <Button variant="outline" size="sm" onClick={() => onEdit(activity)}>
            <Edit className="h-3 w-3" />
          </Button>
          <Button variant="outline" size="sm" onClick={() => onDelete(activity)}>
            <Trash2 className="h-3 w-3 text-destructive" />
          </Button>
        </div>
      </div>
      {activity.description && <p className="text-sm break-words">{activity.description}</p>}
    </div>
  )
}
