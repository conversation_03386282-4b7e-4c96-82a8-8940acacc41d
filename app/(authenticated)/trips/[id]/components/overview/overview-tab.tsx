"use client"

import { useState, use<PERSON>emo } from "react"
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Calendar, DollarSign, Users, User as UserIcon, Edit } from "lucide-react"
import { TripProgressWidget } from "./progress-widget"
import { TripWeatherWidget } from "./weather-widget"
import { TripSavingsWidget } from "./savings-widget"
import { SquadSavingsReadinessWidget } from "./squad-savings-readiness-widget"
import { TripDetailsEdit } from "./details-edit"
import { User } from "@/lib/domains/user/user.types"
import { Trip } from "@/lib/domains/trip/trip.types"
import { Task } from "@/lib/domains/task/task.types"
import { UserTrip } from "@/lib/domains"
import { useTripStore } from "@/lib/domains/trip/trip.store"

interface TripOverviewTabProps {
  trip: Trip
  attendees: UserTrip[]
  attendeesDetails: User[]
  pendingTasks: Task[]
  isLeader: boolean
  squadName?: string
  userAttendingTrip: boolean
  isTripOngoing?: boolean
  isActive?: boolean
}

export function TripOverviewTab({
  trip,
  attendees,
  attendeesDetails,
  pendingTasks,
  isLeader,
  userAttendingTrip,
}: TripOverviewTabProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [tripData, setTripData] = useState<Trip>(trip)
  const { updateTrip } = useTripStore()

  // Memoize date conversions to prevent unnecessary re-renders
  const memoizedStartDate = useMemo(() => {
    return tripData.startDate ? tripData.startDate.toDate() : undefined
  }, [tripData.startDate])

  const memoizedEndDate = useMemo(() => {
    return tripData.endDate ? tripData.endDate.toDate() : undefined
  }, [tripData.endDate])

  // Format dates in a more readable way
  const formatReadableDate = (date: Date) => {
    return date.toLocaleDateString(undefined, {
      weekday: "short",
      month: "long",
      day: "numeric",
      year: "numeric",
    })
  }

  const handleSaveTrip = async (updatedTrip: Trip) => {
    const success = await updateTrip(updatedTrip.id, updatedTrip)
    if (success) {
      setTripData(updatedTrip)
      setIsEditing(false)
    }
  }

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Row 1: Trip Details (left) and Trip Progress (right) */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Trip Details Card */}
        {isEditing ? (
          <TripDetailsEdit
            trip={tripData}
            onCancel={() => setIsEditing(false)}
            onSave={handleSaveTrip}
          />
        ) : (
          <Card>
            <CardHeader className="pb-2">
              <div className="flex justify-between">
                <CardTitle>Trip Details</CardTitle>
                {isLeader && (
                  <Button variant="ghost" size="sm" onClick={() => setIsEditing(true)}>
                    <Edit className="h-4 w-4 mr-2" /> Edit
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <h3 className="font-medium text-base md:text-lg">{tripData.name}</h3>
              <p className="text-muted-foreground mt-1">
                {tripData.description || "No description provided."}
              </p>

              <div className="grid grid-cols-2 gap-3 md:gap-4 mt-3 md:mt-4">
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center">
                    <Calendar className="h-4 w-4 mr-2" /> Dates
                  </div>
                  <div className="text-sm">
                    {tripData.startDate && tripData.endDate ? (
                      <>
                        <span className="block">
                          {formatReadableDate(tripData.startDate.toDate())}
                        </span>
                        <span className="block text-muted-foreground">to</span>
                        <span className="block">
                          {formatReadableDate(tripData.endDate.toDate())}
                        </span>
                      </>
                    ) : (
                      "No dates set"
                    )}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center">
                    <DollarSign className="h-4 w-4 mr-2" /> Budget
                  </div>
                  <p>$ {tripData.budget}</p>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center">
                    <Users className="h-4 w-4 mr-2" /> Attendees
                  </div>
                  <p>{attendees.length} people</p>
                </div>
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground flex items-center">
                    <UserIcon className="h-4 w-4 mr-2" /> Organized by
                  </div>
                  <p>{isLeader ? "You" : "Squad Leader"}</p>
                </div>
              </div>

              <div className="mt-3 md:mt-4">
                <div className="flex -space-x-2 overflow-hidden">
                  {attendeesDetails.slice(0, 5).map((attendee, index) => (
                    <Avatar key={index} className="border-2 border-background">
                      <AvatarImage
                        src={attendee.photoURL || undefined}
                        alt={attendee.displayName || "User"}
                      />
                      <AvatarFallback>{attendee.displayName?.charAt(0) || "U"}</AvatarFallback>
                    </Avatar>
                  ))}
                  {attendees.length > 5 && (
                    <div className="flex items-center justify-center w-10 h-10 rounded-full border-2 border-background bg-muted text-muted-foreground text-xs">
                      +{attendees.length - 5}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Trip Progress Widget - Only show if user is attending */}
        {userAttendingTrip && (
          <TripProgressWidget
            tripId={tripData.id}
            tasksCompleted={tripData.tasksCompleted}
            totalTasks={tripData.totalTasks}
            pendingTasks={pendingTasks.slice(0, 5)} /* Show up to 5 tasks */
          />
        )}
      </div>

      {/* Only show these components if user is attending */}
      {userAttendingTrip && (
        <>
          {/* Row 2: Your Trip Savings (left) and Weather Forecast (right) */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <TripSavingsWidget
              tripId={tripData.id}
              tripName={tripData.destination}
              totalCost={tripData.budget}
              startDate={memoizedStartDate}
            />

            <TripWeatherWidget
              destination={tripData.destination}
              startDate={memoizedStartDate}
              endDate={memoizedEndDate}
            />
          </div>

          {/* Row 3: Squad Savings (full width) */}
          <SquadSavingsReadinessWidget
            tripId={tripData.id}
            tripName={tripData.destination}
            totalCost={6000}
            isOrganizer={isLeader}
          />
        </>
      )}
    </div>
  )
}
