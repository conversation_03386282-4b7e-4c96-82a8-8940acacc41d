"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, AlertCircle, Edit, Trash2 } from "lucide-react"
import { Task } from "@/lib/domains/task/task.types"
import { User } from "@/lib/domains/user/user.types"
import { TaskAffiliateLinks } from "./task-affiliate-links"
import { Timestamp } from "firebase/firestore"
import { <PERSON><PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface TaskListItemProps {
  task: Task & { assignee?: User }
  isTaskOwner: boolean
  isLeader: boolean
  onComplete: (taskId: string, completed: boolean) => void
  onEdit: (task: Task) => void
  onDelete: (task: Task) => void
  isUpdating: boolean
}

export function TaskListItem({
  task,
  isTaskOwner,
  isLeader,
  onComplete,
  onEdit,
  onDelete,
  isUpdating,
}: TaskListItemProps) {
  // Check if a task is overdue
  const isOverdue = (task: Task) => {
    if (!task.dueDate || task.completed) return false

    try {
      let date: Date | null = null

      // Check if dueDate is a Timestamp object with toDate method
      if (typeof task.dueDate.toDate === "function") {
        try {
          date = task.dueDate.toDate()
        } catch (e) {
          console.error("Error converting Timestamp to Date:", e)
        }
      }
      // If it's already a Date object
      else if (task.dueDate instanceof Date) {
        date = task.dueDate
      }
      // If it's a number (timestamp in milliseconds)
      else if (typeof task.dueDate === "number") {
        date = new Date(task.dueDate)
      }
      // If it's a string (ISO date or other format)
      else if (typeof task.dueDate === "string") {
        date = new Date(task.dueDate)
      }
      // If it's an object with seconds and nanoseconds (Firestore Timestamp-like)
      else if (task.dueDate && typeof task.dueDate === "object" && "seconds" in task.dueDate) {
        try {
          // Convert Firestore Timestamp-like object to milliseconds
          const seconds = (task.dueDate as any).seconds
          const nanoseconds = (task.dueDate as any).nanoseconds || 0
          date = new Date(seconds * 1000 + nanoseconds / 1000000)
        } catch (e) {
          console.error("Error converting Timestamp-like object to Date:", e)
        }
      }

      // Check if the date is valid and if it's in the past
      return date && !isNaN(date.getTime()) ? date < new Date() : false
    } catch (error) {
      console.error("Error checking if task is overdue:", error, task.dueDate)
      return false
    }
  }

  // Format due date
  const formatDueDate = (dueDate: Timestamp | null) => {
    if (!dueDate) return "No due date"

    try {
      let date: Date | null = null

      // Check if dueDate is a Timestamp object with toDate method
      if (typeof dueDate.toDate === "function") {
        try {
          date = dueDate.toDate()
        } catch (e) {
          console.error("Error converting Timestamp to Date:", e)
        }
      }
      // If it's already a Date object
      else if (dueDate instanceof Date) {
        date = dueDate
      }
      // If it's a number (timestamp in milliseconds)
      else if (typeof dueDate === "number") {
        date = new Date(dueDate)
      }
      // If it's a string (ISO date or other format)
      else if (typeof dueDate === "string") {
        date = new Date(dueDate)
      }
      // If it's an object with seconds and nanoseconds (Firestore Timestamp-like)
      else if (dueDate && typeof dueDate === "object" && "seconds" in dueDate) {
        try {
          // Convert Firestore Timestamp-like object to milliseconds
          const seconds = (dueDate as any).seconds
          const nanoseconds = (dueDate as any).nanoseconds || 0
          date = new Date(seconds * 1000 + nanoseconds / 1000000)
        } catch (e) {
          console.error("Error converting Timestamp-like object to Date:", e)
        }
      }

      // Check if the date is valid
      if (date && !isNaN(date.getTime())) {
        return date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        })
      } else {
        return "No due date"
      }
    } catch (error) {
      console.error("Error formatting due date:", error, dueDate)
      return "No due date"
    }
  }

  // Get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case "planning":
        return "bg-blue-100 text-blue-800"
      case "booking":
        return "bg-purple-100 text-purple-800"
      case "preparation":
        return "bg-green-100 text-green-800"
      case "coordination":
        return "bg-yellow-100 text-yellow-800"
      case "during-trip":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div
      className={`flex items-start gap-3 p-4 rounded-lg border ${
        task.completed ? "bg-muted/50" : isOverdue(task) ? "bg-red-50 border-red-200" : ""
      }`}
    >
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Checkbox
              checked={task.completed}
              onCheckedChange={() => onComplete(task.id, task.completed)}
              className="mt-1"
              disabled={!(isTaskOwner || isLeader) || isUpdating}
            />
          </TooltipTrigger>
          <TooltipContent>
            {isTaskOwner || isLeader
              ? task.completed
                ? "Mark as incomplete"
                : "Mark as complete"
              : "Only the task owner, task assignee, or trip leader can complete this task"}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <div className="flex-1 min-w-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <h3
            className={`font-medium ${task.completed ? "line-through text-muted-foreground" : ""}`}
          >
            {task.title}
          </h3>
          <div className="flex flex-wrap gap-2">
            <Badge className={getCategoryColor(task.category)}>
              {task.category.charAt(0).toUpperCase() + task.category.slice(1)}
            </Badge>
            {task.dueDate && (
              <Badge variant="outline" className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {formatDueDate(task.dueDate)}
              </Badge>
            )}
            {isOverdue(task) && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                Overdue
              </Badge>
            )}
          </div>
        </div>
        {task.description && (
          <p className={`text-sm mt-1 ${task.completed ? "text-muted-foreground" : ""}`}>
            {task.description}
          </p>
        )}

        {/* Affiliate links for this task */}
        {!task.completed && (
          <div className="mt-2">
            <TaskAffiliateLinks task={task} />
          </div>
        )}

        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center">
            {/* Show assignee information from either the assignee object or directly from task */}
            {task.assignee && (
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarImage
                    src={task.assignee?.photoURL || ""}
                    alt={task.assignee?.displayName || "User"}
                  />
                  <AvatarFallback>{(task.assignee?.displayName || "U").charAt(0)}</AvatarFallback>
                </Avatar>
                <span className="text-sm text-muted-foreground">
                  {task.assignee?.displayName || "Unknown User"}
                </span>
              </div>
            )}
          </div>
          {(isTaskOwner || isLeader) && (
            <div className="flex gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" onClick={() => onEdit(task)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Edit task</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" onClick={() => onDelete(task)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Delete task</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
