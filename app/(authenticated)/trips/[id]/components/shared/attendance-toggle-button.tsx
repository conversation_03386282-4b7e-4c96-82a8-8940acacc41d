"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { UserCheck, UserX, Check, HelpCircle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { InlineLoading } from "@/components/inline-loading"
import { cn } from "@/lib/utils"
import { UserTripStatus } from "@/lib/domains/user-trip/user-trip.types"
import { useUserTripStore } from "@/lib/domains/user-trip/user-trip.store"

interface AttendanceToggleButtonProps {
  tripId: string
  status?: UserTripStatus | null
  disabled?: boolean
}

export function AttendanceToggleButton({
  tripId,
  status,
  disabled = false,
}: AttendanceToggleButtonProps) {
  const user = useUser()
  const { toast } = useToast()
  const { updateUserTripStatus } = useUserTripStore()
  const [updating, setUpdating] = useState(false)

  const handleStatusChange = async (newStatus: UserTripStatus) => {
    if (!user) return

    try {
      setUpdating(true)
      const success = await updateUserTripStatus(user.uid, tripId, newStatus)

      if (success) {
        let title = "Status updated"
        let description = "Your attendance status has been updated"

        if (newStatus === "going") {
          title = "You're going!"
          description = "You've been added to the trip attendees"
        } else if (newStatus === "not-going") {
          description = "You've been marked as not attending this trip"
        } else if (newStatus === "undecided") {
          description = "You've been marked as undecided for this trip"
        }

        toast({ title, description })
      }
    } catch (error) {
      console.error("Error updating trip status:", error)
      toast({
        title: "Error",
        description: "Failed to update your attendance status",
        variant: "destructive",
      })
    } finally {
      setUpdating(false)
    }
  }

  if (!status) {
    return (
      <div className="flex items-center gap-3">
        <Badge variant="outline" className="px-3 py-1 text-xs font-medium animate-pulse">
          Loading...
        </Badge>
        <div className="animate-pulse">
          <div className="h-9 w-28 bg-muted rounded"></div>
        </div>
      </div>
    )
  }

  const isGoing = status === "going"
  const isNotGoing = status === "not-going"
  const isUndecided = status === "undecided"

  // Get the badge color and text based on status
  const getBadgeContent = () => {
    if (isGoing) {
      return {
        variant: "default" as const,
        className: "bg-green-500 hover:bg-green-500 text-white",
        icon: <Check className="h-3 w-3 mr-1" />,
        text: "Going",
      }
    } else if (isUndecided) {
      return {
        variant: "outline" as const,
        className: "bg-yellow-100 hover:bg-yellow-100 text-yellow-700 border-yellow-200",
        icon: <HelpCircle className="h-3 w-3 mr-1" />,
        text: "Undecided",
      }
    } else {
      return {
        variant: "outline" as const,
        className: "text-muted-foreground",
        icon: null,
        text: "Not Going",
      }
    }
  }

  const badgeContent = getBadgeContent()

  return (
    <div className="flex items-center gap-3">
      <Badge
        variant={badgeContent.variant}
        className={cn("px-3 py-1 text-xs font-medium transition-all", badgeContent.className)}
      >
        {badgeContent.icon}
        {badgeContent.text}
      </Badge>

      {/* Show different buttons based on current status */}
      {isGoing && (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange("undecided")}
            disabled={updating || disabled}
            className={cn(
              "border transition-all",
              "bg-background hover:bg-yellow-50 hover:text-yellow-700 hover:border-yellow-200",
              updating && "opacity-70 cursor-wait"
            )}
          >
            {updating ? (
              <>
                <InlineLoading size="small" />
                <span className="ml-2">Updating...</span>
              </>
            ) : (
              <>
                <HelpCircle className="h-4 w-4 mr-2" />
                Not Sure
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange("not-going")}
            disabled={updating || disabled}
            className={cn(
              "border transition-all",
              "bg-background hover:bg-red-50 hover:text-red-700 hover:border-red-200",
              updating && "opacity-70 cursor-wait"
            )}
          >
            {updating ? (
              <>
                <InlineLoading size="small" />
                <span className="ml-2">Leaving...</span>
              </>
            ) : (
              <>
                <UserX className="h-4 w-4 mr-2" />
                Not Going
              </>
            )}
          </Button>
        </div>
      )}

      {isUndecided && (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange("going")}
            disabled={updating || disabled}
            className={cn(
              "border transition-all",
              "bg-background hover:bg-green-50 hover:text-green-700 hover:border-green-200",
              updating && "opacity-70 cursor-wait"
            )}
          >
            {updating ? (
              <>
                <InlineLoading size="small" />
                <span className="ml-2">Joining...</span>
              </>
            ) : (
              <>
                <UserCheck className="h-4 w-4 mr-2" />
                I'm Going
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange("not-going")}
            disabled={updating || disabled}
            className={cn(
              "border transition-all",
              "bg-background hover:bg-red-50 hover:text-red-700 hover:border-red-200",
              updating && "opacity-70 cursor-wait"
            )}
          >
            {updating ? (
              <>
                <InlineLoading size="small" />
                <span className="ml-2">Leaving...</span>
              </>
            ) : (
              <>
                <UserX className="h-4 w-4 mr-2" />
                Not Going
              </>
            )}
          </Button>
        </div>
      )}

      {isNotGoing && (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange("going")}
            disabled={updating || disabled}
            className={cn(
              "border transition-all",
              "bg-background hover:bg-green-50 hover:text-green-700 hover:border-green-200",
              updating && "opacity-70 cursor-wait"
            )}
          >
            {updating ? (
              <>
                <InlineLoading size="small" />
                <span className="ml-2">Joining...</span>
              </>
            ) : (
              <>
                <UserCheck className="h-4 w-4 mr-2" />
                I'm Going
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleStatusChange("undecided")}
            disabled={updating || disabled}
            className={cn(
              "border transition-all",
              "bg-background hover:bg-yellow-50 hover:text-yellow-700 hover:border-yellow-200",
              updating && "opacity-70 cursor-wait"
            )}
          >
            {updating ? (
              <>
                <InlineLoading size="small" />
                <span className="ml-2">Updating...</span>
              </>
            ) : (
              <>
                <HelpCircle className="h-4 w-4 mr-2" />
                Not Sure
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
