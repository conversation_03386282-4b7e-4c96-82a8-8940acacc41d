"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { DashboardSkeleton } from "./components/DashboardSkeleton"
import { SquadsTab } from "./components/SquadsTab"
import { UpcomingTripsTab } from "./components/UpcomingTripsTab"
import { PastTripsTab } from "./components/PastTripsTab"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAllTrips } from "@/lib/domains/trip/trip.realtime.hooks"

export default function DashboardPage() {
  const { user, loading: authLoading } = useAuthStatus()

  // Get squads with real-time updates
  const { squads, loading: squadsLoading } = useRealtimeUserSquads()

  // Get all trips with real-time updates
  const { upcomingTrips, pastTrips, loading: tripsLoading } = useRealtimeUserAllTrips()

  // Determine overall loading state
  const isLoading = authLoading || squadsLoading || tripsLoading

  if (isLoading && !squads.length && !upcomingTrips.length && !pastTrips.length) {
    return <DashboardSkeleton />
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6">
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">Welcome back, {user?.displayName || "Friend"}!</p>
          </div>

          <Tabs defaultValue="squads" className="space-y-4">
            <TabsList className="w-full">
              <TabsTrigger value="squads" className="flex-1 md:flex-initial">
                My Squads
              </TabsTrigger>
              <TabsTrigger value="trips" className="flex-1 md:flex-initial">
                Upcoming Trips
              </TabsTrigger>
              <TabsTrigger value="past" className="flex-1 md:flex-initial">
                Past Trips
              </TabsTrigger>
            </TabsList>

            <TabsContent value="squads">
              <SquadsTab squads={squads} upcomingTrips={upcomingTrips} loading={isLoading} />
            </TabsContent>

            <TabsContent value="trips">
              <UpcomingTripsTab squads={squads} upcomingTrips={upcomingTrips} loading={isLoading} />
            </TabsContent>

            <TabsContent value="past">
              <PastTripsTab squads={squads} pastTrips={pastTrips} loading={isLoading} />
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}
