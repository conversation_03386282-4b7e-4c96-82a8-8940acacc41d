"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, Compass, Clock, Plus } from "lucide-react"
import { OptimizedImage } from "@/components/optimized-image"
import { formatDateRange } from "./utils"
import { Squad } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"

interface UpcomingTripsTabProps {
  squads: Squad[]
  upcomingTrips: Trip[]
  loading: boolean
}

export function UpcomingTripsTab({ squads, upcomingTrips, loading }: UpcomingTripsTabProps) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Upcoming Trips</h2>
        <Link href="/trips/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Create Trip
          </Button>
        </Link>
      </div>

      {upcomingTrips.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {upcomingTrips.map((trip) => (
            <Link href={`/trips/${trip.id}`} key={trip.id}>
              <Card className="h-full hover:shadow-md transition-shadow">
                <div className="aspect-video relative overflow-hidden rounded-t-lg">
                  <OptimizedImage
                    src={
                      trip.locationThumbnail ||
                      trip.image ||
                      "/placeholder.svg?height=200&width=400"
                    }
                    alt={trip.destination}
                    aspectRatio="video"
                    className="rounded-t-lg"
                    priority
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                    <h3 className="text-white font-bold">{trip.destination}</h3>
                    <p className="text-white/80 text-sm">
                      {formatDateRange(trip.startDate, trip.endDate)}
                    </p>
                  </div>
                </div>
                <CardContent className="pt-4">
                  <div className="flex justify-between items-center mb-2">
                    <Badge>
                      {squads.find((s) => s.id === trip.squadId)?.name || "Unknown Squad"}
                    </Badge>
                    <div className="text-sm text-muted-foreground flex items-center">
                      <Users className="h-3 w-3 mr-1" />{" "}
                      {Array.isArray(trip.attendees) ? trip.attendees.length : 0}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm">
                        <span className="font-medium">{trip.tasksCompleted || 0}</span>
                        <span className="text-muted-foreground">
                          {" "}
                          / {trip.totalTasks || 0} tasks completed
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Compass className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">No Upcoming Trips</p>
            <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
              Create a squad and start planning your next adventure
            </p>
            <Link href="/squads/create">
              <Button>Create a Squad</Button>
            </Link>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
