"use client"

import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Star } from "lucide-react"
import { OptimizedImage } from "@/components/optimized-image"
import { formatDateRange } from "./utils"
import { Squad } from "@/lib/domains/squad/squad.types"
import { Trip } from "@/lib/domains/trip/trip.types"

interface PastTripsTabProps {
  squads: Squad[]
  pastTrips: Trip[]
  loading: boolean
}

export function PastTripsTab({ squads, pastTrips, loading }: PastTripsTabProps) {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Past Trips</h2>

      {pastTrips.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {pastTrips.map((trip) => (
            <Link href={`/trips/${trip.id}`} key={trip.id}>
              <Card className="h-full hover:shadow-md transition-shadow">
                <div className="aspect-video relative overflow-hidden rounded-t-lg">
                  <OptimizedImage
                    src={
                      trip.locationThumbnail ||
                      trip.image ||
                      "/placeholder.svg?height=200&width=400"
                    }
                    alt={trip.destination}
                    aspectRatio="video"
                    className="rounded-t-lg"
                    priority
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                    <h3 className="text-white font-bold">{trip.destination}</h3>
                    <p className="text-white/80 text-sm">
                      {formatDateRange(trip.startDate, trip.endDate)}
                    </p>
                  </div>
                </div>
                <CardContent className="pt-4">
                  <div className="flex justify-between items-center mb-2">
                    <Badge>
                      {squads.find((s) => s.id === trip.squadId)?.name || "Unknown Squad"}
                    </Badge>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${i < 4 ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"}`}
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {trip.description || "No description available"}
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Calendar className="h-6 w-6 text-primary" />
            </div>
            <p className="font-medium text-center">No Past Trips</p>
            <p className="text-sm text-muted-foreground text-center mt-1">
              Your completed trips will appear here
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
