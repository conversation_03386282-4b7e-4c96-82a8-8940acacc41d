"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import { PageLoading } from "@/components/page-loading"
import { useToast } from "@/components/ui/use-toast"
import { SubscriptionInitializer } from "@/components/subscription-initializer"
import { UserDataInitializer } from "@/components/user-data-initializer"
import { SettingsInitializer } from "@/components/settings-initializer"

export default function AuthenticatedLayout({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuthStatus()
  const { toast } = useToast()
  const router = useRouter()

  useEffect(() => {
    // Only redirect if we're not loading and there's no user
    if (!loading && !user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to access this page.",
        variant: "warning",
      })
      router.push("/login")
    }
  }, [user, loading, router])

  // Show loading state while checking authentication
  if (loading) {
    return <PageLoading message="Checking authentication..." />
  }

  // Don't render children until we confirm the user is authenticated
  if (!user) {
    return null
  }

  // User is authenticated, render the children with domain-specific initializers
  return (
    <>
      {/* Initialize domain-specific stores */}
      <UserDataInitializer />
      <SubscriptionInitializer />
      <SettingsInitializer />

      {children}
    </>
  )
}
