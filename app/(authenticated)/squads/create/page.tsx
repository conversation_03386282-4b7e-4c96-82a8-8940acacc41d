"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import {
  useIsUserSubscribed,
  useCanCreateMoreSquads,
  useUserSubscriptionWithInit,
} from "@/lib/domains/user-subscription/user-subscription.hooks"
import { createSquad } from "@/lib/firebase-service"
import { Loader2, AlertCircle } from "lucide-react"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import Link from "next/link"
import { SubscriptionErrorType, getSubscriptionErrorAlert } from "@/lib/subscription-errors"

interface SquadFormData {
  name: string
  description: string
}

export default function CreateSquadPage() {
  const user = useUser()
  const isSubscribed = useIsUserSubscribed()
  const canCreateMoreSquadsFunc = useCanCreateMoreSquads()
  const { handleSubscriptionError } = useUserSubscriptionWithInit()
  const router = useRouter()
  const { toast } = useToast()
  const [submitting, setSubmitting] = useState(false)
  const [loading, setLoading] = useState(true)
  const [canCreateSquad, setCanCreateSquad] = useState(true)
  const [formData, setFormData] = useState<SquadFormData>({
    name: "",
    description: "",
  })

  // Check if user can create more squads
  useEffect(() => {
    const checkSubscriptionLimits = async () => {
      if (!user) return

      try {
        setLoading(true)
        const canCreate = await canCreateMoreSquadsFunc(user.uid)
        setCanCreateSquad(canCreate)
      } catch (error) {
        console.error("Error checking subscription limits:", error)
        // Default to allowing creation, but log the error
        setCanCreateSquad(true)
      } finally {
        setLoading(false)
      }
    }

    checkSubscriptionLimits()
  }, [user, canCreateMoreSquadsFunc])

  const handleChange = (field: keyof SquadFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    if (!formData.name) {
      toast({
        title: "Missing information",
        description: "Please provide a name for your squad.",
        variant: "destructive",
      })
      return
    }

    // Double-check subscription limits before creating
    if (!isSubscribed) {
      const canCreate = await canCreateMoreSquadsFunc(user.uid)
      if (!canCreate) {
        // Use centralized error handling
        handleSubscriptionError(SubscriptionErrorType.MAX_SQUADS_REACHED)
        return
      }
    }

    try {
      setSubmitting(true)

      // Create the squad
      const squadId = await createSquad({
        name: formData.name,
        description: formData.description,
        leaderId: user.uid,
        members: [user.uid],
      })

      toast({
        title: "Squad created!",
        description: "Your squad has been created successfully.",
      })

      router.push(`/squads/${squadId}`)
    } catch (error) {
      console.error("Error creating squad:", error)
      toast({
        title: "Error",
        description: "Failed to create squad. Please try again.",
        variant: "destructive",
      })
      setSubmitting(false)
    }
  }

  if (!user || loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <AppHeader />
        <div className="flex-1 flex">
          <AppSidebar />
          <main className="flex-1 p-6 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </main>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />
      <div className="flex-1 flex">
        <AppSidebar />
        <main className="flex-1 p-6">
          <div className="max-w-2xl mx-auto">
            <h1 className="text-3xl font-bold mb-2">Create a New Squad</h1>
            <p className="text-muted-foreground mb-6">
              A Squad can be anyone you love to travel with, friends, family, your partner, or even
              that one co-worker who always wants in.
            </p>

            {!canCreateSquad && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>
                  {getSubscriptionErrorAlert(SubscriptionErrorType.MAX_SQUADS_REACHED).title}
                </AlertTitle>
                <AlertDescription>
                  {getSubscriptionErrorAlert(SubscriptionErrorType.MAX_SQUADS_REACHED).description}
                  <div className="mt-2">
                    <Link href="/settings?tab=billing" className="underline font-medium">
                      Upgrade to Pro
                    </Link>{" "}
                    to create unlimited squads.
                  </div>
                </AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Squad Details</CardTitle>
                <CardDescription>
                  Create a new squad to plan trips with your partner, spouse, or friends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name">Squad Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleChange("name", e.target.value)}
                        placeholder="The Adventure Crew"
                        required
                      />
                    </div>

                    <div>
                      <Label htmlFor="description">Description (optional)</Label>
                      <Textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => handleChange("description", e.target.value)}
                        placeholder="A group for planning our adventures together..."
                        rows={4}
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" disabled={submitting || !canCreateSquad}>
                      {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Create Squad
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  )
}
