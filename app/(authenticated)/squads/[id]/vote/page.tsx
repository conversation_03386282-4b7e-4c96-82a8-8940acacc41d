"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { ArrowLeft, Check, Users } from "lucide-react"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { TripSuggestionCard } from "@/components/trip-voting/trip-suggestion-card"
import { TripChatSection } from "@/components/trip-voting/trip-chat-section"

export default function SquadVotePage({ params }: { params: { id: string } }) {
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [activeSuggestion, setActiveSuggestion] = useState<string | null>(null)
  const [currentUserId, setCurrentUserId] = useState("current-user")
  const [isLoading, setIsLoading] = useState(true)
  const [consensusReached, setConsensusReached] = useState(false)

  useEffect(() => {
    const fetchSuggestions = async () => {
      try {
        // In a real app, this would be a Firebase query
        // For the MVP, we're using mock data
        const mockSuggestions = [
          {
            id: "s1",
            title: "Yellowstone Adventure",
            destination: "Yellowstone National Park",
            dates: "Aug 15-20, 2023",
            cost: "$1,200 - $1,800 per person",
            image: "/placeholder.svg?height=200&width=400",
            description:
              "Explore the geysers, wildlife, and stunning landscapes of America's first national park. We'll stay in a cabin near the park entrance and spend our days hiking, wildlife watching, and enjoying the natural wonders.",
            tags: ["Nature", "Adventure"],
            votes: [
              {
                userId: "current-user",
                name: "You",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: null,
              },
              {
                userId: "u2",
                name: "Mike Smith",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "yes",
              },
              {
                userId: "u3",
                name: "Alex Johnson",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "yes",
              },
              {
                userId: "u4",
                name: "Chris Williams",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: null,
              },
              {
                userId: "u5",
                name: "David Brown",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "no",
              },
            ],
          },
          {
            id: "s2",
            title: "New Orleans Weekend",
            destination: "New Orleans",
            dates: "Sep 5-10, 2023",
            cost: "$800 - $1,200 per person",
            image: "/placeholder.svg?height=200&width=400",
            description:
              "Experience the vibrant culture, music, and cuisine of the Big Easy. We'll stay in the French Quarter and explore the city's rich history, enjoy live jazz, and sample delicious Creole and Cajun food.",
            tags: ["Culture", "Food"],
            votes: [
              {
                userId: "current-user",
                name: "You",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: null,
              },
              {
                userId: "u2",
                name: "Mike Smith",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "yes",
              },
              {
                userId: "u3",
                name: "Alex Johnson",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "yes",
              },
              {
                userId: "u4",
                name: "Chris Williams",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "yes",
              },
              {
                userId: "u5",
                name: "David Brown",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "yes",
              },
            ],
          },
          {
            id: "s3",
            title: "San Diego Beach Trip",
            destination: "San Diego",
            dates: "Oct 1-5, 2023",
            cost: "$1,000 - $1,500 per person",
            image: "/placeholder.svg?height=200&width=400",
            description:
              "Enjoy perfect weather, beautiful beaches, and laid-back vibes in Southern California. We'll stay near the beach, visit the famous San Diego Zoo, explore Balboa Park, and relax on the sand.",
            tags: ["Beach", "Relaxation"],
            votes: [
              {
                userId: "current-user",
                name: "You",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: null,
              },
              {
                userId: "u2",
                name: "Mike Smith",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "no",
              },
              {
                userId: "u3",
                name: "Alex Johnson",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "no",
              },
              {
                userId: "u4",
                name: "Chris Williams",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "yes",
              },
              {
                userId: "u5",
                name: "David Brown",
                avatar: "/placeholder.svg?height=40&width=40",
                vote: "no",
              },
            ],
          },
        ]

        setSuggestions(mockSuggestions)
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching suggestions:", error)
        setIsLoading(false)
      }
    }

    fetchSuggestions()
  }, [])

  const handleVote = (suggestionId: string, vote: "yes" | "no") => {
    setSuggestions(
      suggestions.map((suggestion) => {
        if (suggestion.id === suggestionId) {
          const updatedVotes = suggestion.votes.map((v: any) =>
            v.userId === currentUserId ? { ...v, vote } : v
          )

          // Check if consensus is reached
          const allVoted = updatedVotes.every((v: any) => v.vote !== null)
          const consensus = updatedVotes.every((v: any) => v.vote === "yes")

          if (allVoted && consensus) {
            setConsensusReached(true)
          }

          return {
            ...suggestion,
            votes: updatedVotes,
          }
        }
        return suggestion
      })
    )
  }

  const handleOpenChat = (suggestionId: string) => {
    setActiveSuggestion(suggestionId)
  }

  const activeSuggestionData = suggestions.find((s) => s.id === activeSuggestion)

  // Calculate voting progress
  const totalMembers = suggestions[0]?.votes.length || 0
  const votedMembers = suggestions.reduce((count, suggestion) => {
    const userVote = suggestion.votes.find((v: any) => v.userId === currentUserId)?.vote
    return userVote ? count + 1 : count
  }, 0)

  const votingProgress = totalMembers > 0 ? (votedMembers / suggestions.length) * 100 : 0

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6 flex items-center">
            <Link href={`/squads/${params.id}`} className="mr-4">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-5 w-5" />
              </Button>
            </Link>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <h1 className="text-3xl font-bold">Trip Voting</h1>
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-muted-foreground" />
                  <span className="text-muted-foreground">5 members</span>
                </div>
              </div>
              <p className="text-muted-foreground">Vote on trip suggestions for your squad</p>
            </div>
          </div>

          {consensusReached ? (
            <Card className="mb-6 bg-green-50 border-green-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <Check className="h-5 w-5" />
                  Consensus Reached!
                </CardTitle>
                <CardDescription className="text-green-600">
                  Your squad has agreed on a trip. Let's start planning!
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <Button>Continue to Trip Planning</Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card className="mb-6">
              <CardHeader className="pb-2">
                <CardTitle>Your Voting Progress</CardTitle>
                <CardDescription>
                  Vote on all trip suggestions to help your squad reach a consensus
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>
                      {votedMembers} of {suggestions.length} suggestions voted on
                    </span>
                    <span>{Math.round(votingProgress)}% complete</span>
                  </div>
                  <Progress value={votingProgress} className="h-2" />
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className={`space-y-6 ${activeSuggestion ? "lg:col-span-2" : "lg:col-span-3"}`}>
              <Tabs defaultValue="all">
                <TabsList className="w-full">
                  <TabsTrigger value="all" className="flex-1 md:flex-initial">
                    All Suggestions ({suggestions.length})
                  </TabsTrigger>
                  <TabsTrigger value="voted" className="flex-1 md:flex-initial">
                    Voted ({votedMembers})
                  </TabsTrigger>
                  <TabsTrigger value="pending" className="flex-1 md:flex-initial">
                    Pending ({suggestions.length - votedMembers})
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="all" className="mt-6 space-y-6">
                  {suggestions.map((suggestion) => (
                    <TripSuggestionCard
                      key={suggestion.id}
                      suggestion={suggestion}
                      currentUserId={currentUserId}
                      onVote={handleVote}
                      onOpenChat={handleOpenChat}
                    />
                  ))}
                </TabsContent>
                <TabsContent value="voted" className="mt-6 space-y-6">
                  {suggestions
                    .filter(
                      (s) => s.votes.find((v: any) => v.userId === currentUserId)?.vote !== null
                    )
                    .map((suggestion) => (
                      <TripSuggestionCard
                        key={suggestion.id}
                        suggestion={suggestion}
                        currentUserId={currentUserId}
                        onVote={handleVote}
                        onOpenChat={handleOpenChat}
                      />
                    ))}
                </TabsContent>
                <TabsContent value="pending" className="mt-6 space-y-6">
                  {suggestions
                    .filter(
                      (s) => s.votes.find((v: any) => v.userId === currentUserId)?.vote === null
                    )
                    .map((suggestion) => (
                      <TripSuggestionCard
                        key={suggestion.id}
                        suggestion={suggestion}
                        currentUserId={currentUserId}
                        onVote={handleVote}
                        onOpenChat={handleOpenChat}
                      />
                    ))}
                </TabsContent>
              </Tabs>
            </div>

            {activeSuggestion && activeSuggestionData && (
              <div className="lg:col-span-1">
                <TripChatSection
                  suggestion={activeSuggestionData}
                  onClose={() => setActiveSuggestion(null)}
                />
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}
