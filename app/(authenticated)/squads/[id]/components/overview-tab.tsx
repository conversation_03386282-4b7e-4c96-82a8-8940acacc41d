"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

import { Calendar, Compass, MapPin, Plus, Star, Users } from "lucide-react"
import { type Trip } from "@/lib/firebase-service"
import { TripIdeasSection } from "./trip-ideas-section"
import { OptimizedImage } from "@/components/optimized-image"

interface OverviewTabProps {
  upcomingTrips: Trip[]
  pastTrips: Trip[]
}

export function OverviewTab({ upcomingTrips, pastTrips }: OverviewTabProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Upcoming Trip</CardTitle>
            <CardDescription>Your next adventure with this squad</CardDescription>
          </CardHeader>
          {upcomingTrips.length > 0 ? (
            <>
              <CardContent className="p-0">
                <div className="aspect-video relative overflow-hidden">
                  <OptimizedImage
                    src={
                      upcomingTrips[0].locationThumbnail ||
                      upcomingTrips[0].image ||
                      "/placeholder.svg?height=200&width=600"
                    }
                    alt={upcomingTrips[0].destination}
                    aspectRatio="video"
                    className="rounded-t-lg w-full h-full object-cover"
                    priority
                  />
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4 z-10">
                    <h3 className="text-white text-xl font-bold">{upcomingTrips[0].destination}</h3>
                    <p className="text-white/80 text-sm">
                      {upcomingTrips[0].startDate.toDate().toLocaleDateString()} -{" "}
                      {upcomingTrips[0].endDate.toDate().toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {upcomingTrips[0].startDate.toDate().toLocaleDateString()} -{" "}
                        {upcomingTrips[0].endDate.toDate().toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{upcomingTrips[0].attendees.length} attending</span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Trip Preparation</h4>
                      <span className="text-sm text-muted-foreground">
                        {upcomingTrips[0].tasksCompleted} of {upcomingTrips[0].totalTasks} tasks
                        completed
                      </span>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div
                        className="h-full bg-primary"
                        style={{
                          width: `${(upcomingTrips[0].tasksCompleted / Math.max(upcomingTrips[0].totalTasks, 1)) * 100}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Link href={`/trips/${upcomingTrips[0].id}`} className="w-full">
                  <Button className="w-full">View Trip Details</Button>
                </Link>
              </CardFooter>
            </>
          ) : (
            <CardContent className="flex flex-col items-center justify-center p-6">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Compass className="h-6 w-6 text-primary" />
              </div>
              <p className="font-medium text-center">No Upcoming Trips</p>
              <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
                Start planning your next trip! Trips can range from date nights to summer vacations,
                just add the preferred Squad to your trip!
              </p>
              <Link href="/trips/create">
                <Button>
                  <Plus className="h-4 w-4 mr-2" /> Plan a Trip
                </Button>
              </Link>
            </CardContent>
          )}
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Squad Stats</CardTitle>
            <CardDescription>Trip history and activity</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">Total Trips</h4>
                <span className="font-bold">{upcomingTrips.length + pastTrips.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">Upcoming Trips</h4>
                <span className="font-bold">{upcomingTrips.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">Past Trips</h4>
                <span className="font-bold">{pastTrips.length}</span>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Average Trip Rating</h4>
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${i < 4 ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"}`}
                  />
                ))}
              </div>
            </div>

            {pastTrips.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Most Popular Destination</h4>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-primary" />
                  <span>{pastTrips[0].destination}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <TripIdeasSection />
    </div>
  )
}
