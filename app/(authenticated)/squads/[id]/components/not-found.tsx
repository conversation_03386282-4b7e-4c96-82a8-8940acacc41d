"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"

export function SquadNotFound() {
  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />
      <div className="flex-1 flex">
        <AppSidebar />
        <main className="flex-1 p-6 flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Squad Not Found</CardTitle>
              <CardDescription>
                The squad you're looking for doesn't exist or you don't have access.
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <Link href="/dashboard" className="w-full">
                <Button className="w-full">Return to Dashboard</Button>
              </Link>
            </CardFooter>
          </Card>
        </main>
      </div>
    </div>
  )
}
