"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UserPlus } from "lucide-react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { MemberList } from "./member-list"
import { getUsersFromIds } from "@/lib/firebase-service"
import { isUserSubscribed } from "@/lib/firebase/subscription-service"
import { User } from "@/lib/domains/user/user.types"
import { Squad } from "@/lib/domains/squad/squad.types"
import { PageLoading } from "@/components/page-loading"
import { toast } from "@/components/ui/use-toast"
import { ErrorBoundary } from "@/components/error-boundary"

interface MemberWithSubscription extends User {
  isSubscribed?: boolean
}

interface MembersTabProps {
  squad: Squad
  onInviteClick?: () => void
}

export function MembersTab({ squad, onInviteClick }: MembersTabProps) {
  const user = useUser()
  const squadId = squad.id

  const [members, setMembers] = useState<MemberWithSubscription[]>([])
  const [loadingMembers, setLoadingMembers] = useState(true)
  const [isSquadLead, setIsSquadLead] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [membersPerPage] = useState(10)
  const [error, setError] = useState<Error | null>(null)

  // Fetch members when squad data changes
  useEffect(() => {
    const fetchMembers = async () => {
      try {
        setLoadingMembers(true)
        setError(null)
        const memberIds = squad.members || []
        if (memberIds.length === 0) {
          setMembers([])
          return
        }

        const membersData = await getUsersFromIds(memberIds)

        // Check subscription status for each member
        const membersWithSubscription = await Promise.all(
          membersData.map(async (member) => {
            try {
              const subscribed = await isUserSubscribed(member.uid)
              return { ...member, isSubscribed: subscribed } as MemberWithSubscription
            } catch (error) {
              console.error(`Error checking subscription for ${member.uid}:`, error)
              return { ...member, isSubscribed: false } as MemberWithSubscription
            }
          })
        )

        setMembers(membersWithSubscription)
      } catch (error) {
        console.error("Error fetching squad members:", error)
        setError(error instanceof Error ? error : new Error("Failed to fetch members"))
        toast({
          title: "Error",
          description: "Unable to load members. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoadingMembers(false)
      }
    }

    fetchMembers()

    // Cleanup function
    return () => {
      // Any cleanup needed
    }
  }, [squad.id, squad.members])

  // Check if current user is squad leader
  useEffect(() => {
    if (user) {
      setIsSquadLead(user.uid === squad.leaderId)
    } else {
      setIsSquadLead(false)
    }
  }, [user, squad.leaderId])

  // Get current members for pagination
  const indexOfLastMember = currentPage * membersPerPage
  const indexOfFirstMember = indexOfLastMember - membersPerPage
  const currentMembers = members.slice(indexOfFirstMember, indexOfLastMember)
  const totalPages = Math.ceil(members.length / membersPerPage)

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber)

  if (loadingMembers) {
    return <PageLoading />
  }

  if (error) {
    return (
      <div className="p-4 rounded-md border border-destructive/50 bg-destructive/10">
        <h3 className="font-medium text-destructive mb-2">Error loading members</h3>
        <p className="text-sm text-muted-foreground mb-4">
          {error?.message || "Unable to load members. Please try again."}
        </p>
        <Button onClick={() => window.location.reload()} variant="outline" size="sm">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Squad Members</h2>
          {isSquadLead && onInviteClick && (
            <Button onClick={onInviteClick}>
              <UserPlus className="mr-2 h-4 w-4" /> Invite
            </Button>
          )}
        </div>

        <MemberList
          members={currentMembers}
          squadLeaderId={squad.leaderId}
          isCurrentUserSquadLead={isSquadLead}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={paginate}
        />
      </div>
    </ErrorBoundary>
  )
}
