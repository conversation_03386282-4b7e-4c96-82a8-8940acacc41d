"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal, ChevronLeft, ChevronRight } from "lucide-react"
import { User } from "@/lib/domains/user/user.types"
import { UserDisplay } from "@/components/user-display"
import { SubscriberBadge } from "@/components/subscriber-badge"
import { SkeletonList } from "@/components/skeleton-loader"
import { toast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface MemberWithSubscription extends User {
  isSubscribed?: boolean
}

interface MemberListProps {
  members: MemberWithSubscription[]
  squadLeaderId: string
  isCurrentUserSquadLead: boolean
  currentPage?: number
  totalPages?: number
  onPageChange?: (page: number) => void
}

export function MemberList({
  members,
  squadLeaderId,
  isCurrentUserSquadLead,
  currentPage = 1,
  totalPages = 1,
  onPageChange,
}: MemberListProps) {
  const [loadingStatus, setLoadingStatus] = useState<Record<string, boolean>>({})

  const handleMemberAction = (memberId: string, action: string) => {
    setLoadingStatus((prev) => ({ ...prev, [memberId]: true }))

    // Simulate API call
    setTimeout(() => {
      setLoadingStatus((prev) => ({ ...prev, [memberId]: false }))

      toast({
        title: "Member action",
        description: `${action} action will be implemented soon`,
      })
    }, 1000)
  }

  if (members.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">No members found</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-0">
          <div className="divide-y">
            {members.map((member, index) => (
              <div key={member.uid || index} className="flex items-center justify-between p-4">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <UserDisplay
                      displayName={member.displayName}
                      photoURL={member.photoURL}
                      showBadge={false} /* We'll show the badge separately */
                    />
                    {member.isSubscribed && (
                      <div className="flex items-center bg-amber-50 dark:bg-amber-950 px-2 py-0.5 rounded-full">
                        <SubscriberBadge />
                        <span className="text-xs ml-1 text-amber-600 dark:text-amber-400 font-medium">
                          Pro
                        </span>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground ml-10">{member.email}</p>
                </div>
                <div className="flex items-center gap-2">
                  {member.uid === squadLeaderId && <Badge variant="outline">Squad Lead</Badge>}
                  {isCurrentUserSquadLead && member.uid !== squadLeaderId && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" disabled={loadingStatus[member.uid]}>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleMemberAction(member.uid, "Promote to lead")}
                        >
                          Promote to lead
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleMemberAction(member.uid, "Remove from squad")}
                          className="text-destructive"
                        >
                          Remove from squad
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <span className="text-sm">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange?.(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  )
}
