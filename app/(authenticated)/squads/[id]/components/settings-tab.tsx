"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { type Squad } from "@/lib/firebase-service"

interface SettingsTabProps {
  squad: Squad
  isSquadLead: boolean
}

export function SettingsTab({ squad, isSquadLead }: SettingsTabProps) {
  const [squadName, setSquadName] = useState(squad.name)
  const [squadDescription, setSquadDescription] = useState(squad.description || "")
  const [saving, setSaving] = useState(false)

  const handleSaveChanges = async () => {
    if (!isSquadLead) return

    setSaving(true)
    try {
      // Save squad changes logic would go here
      // For now, just show a toast
      toast({
        title: "Squad settings updated",
        description: "Your squad settings have been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating squad settings:", error)
      toast({
        title: "Error",
        description: "Failed to update squad settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Squad Settings</h2>

      {isSquadLead && (
        <Card>
          <CardHeader>
            <CardTitle>General Settings</CardTitle>
            <CardDescription>Manage your squad details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="squad-name">Squad Name</Label>
              <Input
                id="squad-name"
                value={squadName}
                onChange={(e) => setSquadName(e.target.value)}
                disabled={!isSquadLead || saving}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="squad-description">Description</Label>
              <textarea
                id="squad-description"
                rows={3}
                value={squadDescription}
                onChange={(e) => setSquadDescription(e.target.value)}
                disabled={!isSquadLead || saving}
                className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              ></textarea>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSaveChanges} disabled={!isSquadLead || saving}>
              {saving ? "Saving..." : "Save Changes"}
            </Button>
          </CardFooter>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Danger Zone</CardTitle>
          <CardDescription>Irreversible actions</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center p-4 border rounded-md border-destructive/20 bg-destructive/5">
            <div>
              <h3 className="font-medium">Leave Squad</h3>
              <p className="text-sm text-muted-foreground">Remove yourself from this squad</p>
            </div>
            <Button variant="outline" className="text-destructive border-destructive/50">
              Leave Squad
            </Button>
          </div>

          {isSquadLead && (
            <div className="flex justify-between items-center p-4 border rounded-md border-destructive/20 bg-destructive/5">
              <div>
                <h3 className="font-medium">Delete Squad</h3>
                <p className="text-sm text-muted-foreground">This action cannot be undone</p>
              </div>
              <Button variant="destructive">Delete Squad</Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
