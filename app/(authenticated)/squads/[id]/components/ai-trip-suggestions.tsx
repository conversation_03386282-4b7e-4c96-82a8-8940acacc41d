"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { useAppStore } from "@/lib/store"
import {
  useTravelPreferencesTypes,
  useBudgetRange,
  useAvailabilityPreferences,
  usePreferredTravelSeasons,
  useTravelGroupPreferences,
  useSettingsStore,
} from "@/hooks/use-settings-store"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { getSquad } from "@/lib/firebase-service"
import { getUserPreferences } from "@/lib/firebase/user-preferences-service"
import { useUserAIUsage } from "@/lib/domains/user-ai-usage/user-ai-usage.hooks"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription/user-subscription.hooks"
import { SubscriptionErrorType } from "@/lib/subscription-errors"
import { AIUsageWarning } from "@/components/ai-usage-warning"
import { isValidTravelType } from "@/lib/constants/travel-types"
import {
  Card,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  Card<PERSON>itle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Plus, RefreshCw, Eye } from "lucide-react"
import Link from "next/link"
import { toast } from "@/components/ui/use-toast"
import { generateTripSuggestions } from "@/lib/openai"
import { getLocationImage, getLocationImageByPlaceId, searchPlaces } from "@/lib/google-places"
import { TripSuggestionDetails } from "./trip-suggestion-details"
import { OptimizedImage } from "@/components/optimized-image"

// Define the trip suggestion type
interface TripSuggestion {
  destination: string
  image: string
  tags: string[]
  description: string
  budget: string
  placeId?: string
  formattedAddress?: string
  attribution?: {
    name: string
    photoReference?: string
    username?: string
    link?: string
  }
}

export function AiTripSuggestions() {
  const isSubscribed = useIsUserSubscribed()
  const { incrementUsage, canMakeRequest, usage } = useUserAIUsage(isSubscribed)
  const params = useParams()
  const squadId = params.id as string
  const currentUser = useUser()
  const fetchPreferences = useSettingsStore((state) => state.fetchPreferences)

  // Get current user preferences from settings store
  const travelPreferences = useTravelPreferencesTypes()
  const budgetRange = useBudgetRange()
  const availabilityPreferences = useAvailabilityPreferences()
  const preferredTravelSeasons = usePreferredTravelSeasons()
  const travelGroupPreferences = useTravelGroupPreferences()

  // Squad-related state
  const [squadMembers, setSquadMembers] = useState<string[]>([])
  const [squadPreferences, setSquadPreferences] = useState<{
    travelPreferences: string[]
    budgetRange: [number, number]
    availabilityPreferences: string[]
    preferredTravelSeasons: string[]
    travelGroupPreferences: string[]
  }>({
    travelPreferences: [],
    budgetRange: [500, 2000],
    availabilityPreferences: [],
    preferredTravelSeasons: [],
    travelGroupPreferences: [],
  })

  // UI state
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [suggestions, setSuggestions] = useState<TripSuggestion[]>([])
  const [error, setError] = useState<string | null>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)
  const [selectedSuggestion, setSelectedSuggestion] = useState<TripSuggestion | null>(null)
  const [suggestionsLoaded, setSuggestionsLoaded] = useState(false)
  const [usageWarning, setUsageWarning] = useState<{
    show: boolean
    errorType:
      | SubscriptionErrorType.DAILY_AI_LIMIT_REACHED
      | SubscriptionErrorType.WEEKLY_AI_LIMIT_REACHED
    usageData: {
      daily: number
      weekly: number
      dailyLimit: number
      weeklyLimit: number
    }
  } | null>(null)

  // Get user preferences from store as fallback
  const storeBudgetRange = useAppStore((state) => state.userPreferences.budgetRange)
  const storeTravelTypes = useAppStore((state) => state.userPreferences.travelTypes)

  // Fetch squad members
  useEffect(() => {
    let isMounted = true

    const fetchSquadData = async () => {
      if (!squadId || !currentUser) return

      try {
        // Get squad data
        const squad = await getSquad(squadId)
        if (!squad || !isMounted) return

        // Set squad members
        setSquadMembers(squad.members || [])
      } catch (error) {
        console.error("Error fetching squad data:", error)
      }
    }

    fetchSquadData()

    return () => {
      isMounted = false
    }
  }, [squadId, currentUser])

  // Fetch current user's preferences
  useEffect(() => {
    if (!currentUser) return

    // Make sure current user's preferences are up to date
    fetchPreferences()
  }, [currentUser, fetchPreferences])

  // Fetch squad preferences only when squad members change
  useEffect(() => {
    let isMounted = true

    const fetchSquadPreferences = async () => {
      if (squadMembers.length === 0 || !isMounted) return

      try {
        // Fetch preferences for all squad members
        const preferencesPromises = squadMembers.map((userId) => getUserPreferences(userId))
        const allPreferences = await Promise.all(preferencesPromises)
        const validPreferences = allPreferences.filter(Boolean)

        if (validPreferences.length === 0 || !isMounted) return

        // Combine preferences
        const combinedPreferences = {
          travelPreferences: new Set<string>(),
          budgetRange: [Number.MAX_SAFE_INTEGER, 0] as [number, number],
          availabilityPreferences: new Set<string>(),
          preferredTravelSeasons: new Set<string>(),
          travelGroupPreferences: new Set<string>(),
        }

        validPreferences.forEach((prefs) => {
          // Add travel preferences
          prefs?.travelPreferences?.forEach((pref) =>
            combinedPreferences.travelPreferences.add(pref)
          )

          // Update budget range (min of mins, max of maxes)
          if (Array.isArray(prefs?.budgetRange)) {
            const [min, max] = prefs.budgetRange as [number, number]
            combinedPreferences.budgetRange[0] = Math.min(combinedPreferences.budgetRange[0], min)
            combinedPreferences.budgetRange[1] = Math.max(combinedPreferences.budgetRange[1], max)
          }

          // Add other preferences
          prefs?.availabilityPreferences?.forEach((pref) =>
            combinedPreferences.availabilityPreferences.add(pref)
          )
          prefs?.preferredTravelSeasons?.forEach((pref) =>
            combinedPreferences.preferredTravelSeasons.add(pref)
          )
          prefs?.travelGroupPreferences?.forEach((pref) =>
            combinedPreferences.travelGroupPreferences.add(pref)
          )
        })

        // If min is still MAX_SAFE_INTEGER, reset to default
        if (combinedPreferences.budgetRange[0] === Number.MAX_SAFE_INTEGER) {
          combinedPreferences.budgetRange[0] = 500
        }

        // If max is still 0, reset to default
        if (combinedPreferences.budgetRange[1] === 0) {
          combinedPreferences.budgetRange[1] = 2000
        }

        if (isMounted) {
          // Update squad preferences state
          setSquadPreferences({
            travelPreferences: Array.from(combinedPreferences.travelPreferences),
            budgetRange: combinedPreferences.budgetRange,
            availabilityPreferences: Array.from(combinedPreferences.availabilityPreferences),
            preferredTravelSeasons: Array.from(combinedPreferences.preferredTravelSeasons),
            travelGroupPreferences: Array.from(combinedPreferences.travelGroupPreferences),
          })
        }
      } catch (error) {
        console.error("Error fetching squad preferences:", error)
      }
    }

    fetchSquadPreferences()

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false
    }
  }, [squadMembers]) // Only depend on squadMembers

  // Function to fetch suggestions
  async function fetchSuggestions() {
    try {
      setLoading(true)
      setError(null)

      // Check AI usage before making a request
      const canMakeAIRequest = await canMakeRequest()

      // If we can't make a request, show appropriate warning
      if (!canMakeAIRequest && usage) {
        const usageData = {
          daily: usage.daily,
          weekly: usage.weekly,
          dailyLimit: usage.dailyLimit,
          weeklyLimit: usage.weeklyLimit,
        }

        // Determine if it's a daily or weekly limit that was reached
        if (usage.daily >= usage.dailyLimit) {
          setUsageWarning({
            show: true,
            errorType: SubscriptionErrorType.DAILY_AI_LIMIT_REACHED,
            usageData,
          })
          // Show error instead of fallback suggestions
          setError("You've reached your daily AI usage limit. Please try again tomorrow.")
          setSuggestionsLoaded(true)
          return
        } else if (usage.weekly >= usage.weeklyLimit) {
          setUsageWarning({
            show: true,
            errorType: SubscriptionErrorType.WEEKLY_AI_LIMIT_REACHED,
            usageData,
          })
          // Show error instead of fallback suggestions
          setError("You've reached your weekly AI usage limit. Please try again next week.")
          setSuggestionsLoaded(true)
          return
        }
      }

      // Use squad preferences if available, otherwise fall back to current user preferences
      const userPreferences = {
        travelPreferences:
          squadPreferences.travelPreferences.length > 0
            ? squadPreferences.travelPreferences
            : travelPreferences || storeTravelTypes,
        budgetRange:
          squadPreferences.budgetRange[0] !== Number.MAX_SAFE_INTEGER
            ? squadPreferences.budgetRange
            : budgetRange || storeBudgetRange,
        // Include other preferences if available
        availabilityPreferences:
          squadPreferences.availabilityPreferences.length > 0
            ? squadPreferences.availabilityPreferences
            : availabilityPreferences || [],
        preferredTravelSeasons:
          squadPreferences.preferredTravelSeasons.length > 0
            ? squadPreferences.preferredTravelSeasons
            : preferredTravelSeasons || [],
        travelGroupPreferences:
          squadPreferences.travelGroupPreferences.length > 0
            ? squadPreferences.travelGroupPreferences
            : travelGroupPreferences || [],
      }

      // Call the OpenAI library function directly, passing previous suggestions to avoid repetition
      const result = await generateTripSuggestions(
        userPreferences,
        refreshing ? suggestions : undefined
      )

      // Parse the AI response and convert to TripSuggestion format
      const parsedSuggestions = await parseAiResponse(result)

      // Only increment the user's AI usage counter after successful generation and processing
      await incrementUsage()

      // Filter suggestions to ensure they only use valid travel types
      const validatedSuggestions = parsedSuggestions.map((suggestion) => ({
        ...suggestion,
        tags: suggestion.tags.filter((tag) => isValidTravelType(tag)),
      }))

      setSuggestions(validatedSuggestions)
      setSuggestionsLoaded(true)
    } catch (err) {
      console.error("Error fetching trip suggestions:", err)
      setError("Failed to load trip suggestions. Please try again.")
      toast({
        title: "Error",
        description: "Failed to load trip suggestions. Please try again.",
        variant: "destructive",
      })

      // Show error message instead of fallback suggestions
      setError("Failed to load trip suggestions. Please try again.")
      setSuggestionsLoaded(true)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // Parse the AI response into structured TripSuggestion objects
  const parseAiResponse = async (aiResponse: any): Promise<TripSuggestion[]> => {
    try {
      // Handle string responses (should be rare with direct library call)
      if (typeof aiResponse === "string") {
        try {
          aiResponse = JSON.parse(aiResponse)
        } catch (e) {
          // If we can't parse it as JSON, return fallback suggestions
          console.error("Error parsing string response as JSON:", e)
          return getFallbackSuggestions()
        }
      }

      let suggestions: TripSuggestion[] = []

      // Check if the response has the expected structure
      if (aiResponse.suggestions && Array.isArray(aiResponse.suggestions)) {
        suggestions = aiResponse.suggestions.map((item: any) => ({
          destination: item.destination || item.name || item.location || "",
          image: "/placeholder.svg?height=150&width=300", // Placeholder image initially
          tags: item.tags || item.categories || [],
          description: item.description || "",
          budget: item.budget || item.price || item.cost || "$1,000 - $2,000 per person",
        }))
      } else if (Array.isArray(aiResponse)) {
        suggestions = aiResponse.map((item: any) => ({
          destination: item.destination || item.name || item.location || "",
          image: "/placeholder.svg?height=150&width=300",
          tags: item.tags || item.categories || [],
          description: item.description || "",
          budget: item.budget || item.price || item.cost || "$1,000 - $2,000 per person",
        }))
      } else {
        // If we can't parse it properly, return empty array
        return []
      }

      // Fetch images for each suggestion
      const suggestionsWithImages = await Promise.all(
        suggestions.map(async (suggestion) => {
          if (!suggestion.destination) return suggestion

          try {
            // First, search for the place to get its ID
            const places = await searchPlaces(suggestion.destination)

            if (places.length > 0 && places[0].hasPhoto) {
              // Use the first place with a photo
              const place = places[0]

              const imageResult = await getLocationImageByPlaceId(place.placeId, place.name)

              return {
                ...suggestion,
                placeId: place.placeId, // Store the place ID for future use
                formattedAddress: place.formattedAddress,
                image: imageResult.url,
                attribution: imageResult.attribution,
              }
            } else {
              // Fallback to text search if no place ID found

              const imageResult = await getLocationImage(suggestion.destination)
              return {
                ...suggestion,
                image: imageResult.url,
                attribution: imageResult.attribution,
              }
            }
          } catch (error) {
            console.error(`Error fetching image for ${suggestion.destination}:`, error)
            // Return suggestion with placeholder image
            return {
              ...suggestion,
              image: "/placeholder.svg?height=150&width=300",
            }
          }
        })
      )

      return suggestionsWithImages
    } catch (error) {
      console.error("Error parsing AI response:", error)
      // Return empty array instead of fallback suggestions
      return []
    }
  }

  // Empty function that returns an empty array instead of fallback suggestions
  function getFallbackSuggestions(): TripSuggestion[] {
    return []
  }

  // Handle refresh button click
  function handleRefresh(): void {
    setRefreshing(true)
    fetchSuggestions()
  }

  // Function to check if we're in a solo squad (just the current user)
  const isSoloSquad = (): boolean => {
    return !!(squadMembers.length === 1 && currentUser && squadMembers.includes(currentUser.uid))
  }

  // Effect to refresh suggestions when preferences change
  // Commented out to prevent infinite loading loops
  // useEffect(() => {
  //   // Only refresh if suggestions are already loaded and we're not already loading or refreshing
  //   if (suggestionsLoaded && !loading && !refreshing && squadMembers.length > 0) {
  //     // Use a timeout to prevent immediate refresh and break potential loops
  //     const timer = setTimeout(() => {
  //       handleRefresh()
  //     }, 500)

  //     return () => clearTimeout(timer)
  //   }
  // }, [squadPreferences, suggestionsLoaded, loading, refreshing, squadMembers.length])

  // Load initial suggestions
  // Commented out to prevent auto-loading on initial render
  // useEffect(() => {
  //   // Only load suggestions if we have squad members and haven't loaded them yet
  //   if (squadMembers.length > 0 && !suggestionsLoaded && !loading && !refreshing) {
  //     fetchSuggestions()
  //   }
  // }, [squadMembers.length, suggestionsLoaded, loading, refreshing])

  const handleViewDetails = (suggestion: TripSuggestion) => {
    setSelectedSuggestion(suggestion)
    setDetailsOpen(true)
  }

  return (
    <>
      <TripSuggestionDetails
        suggestion={selectedSuggestion}
        open={detailsOpen}
        onOpenChange={setDetailsOpen}
      />
      {usageWarning && usageWarning.show && (
        <AIUsageWarning
          errorType={usageWarning.errorType}
          usageData={usageWarning.usageData}
          onClose={() => setUsageWarning((prev) => (prev ? { ...prev, show: false } : null))}
        />
      )}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Trip Ideas</CardTitle>
            <CardDescription>
              AI-powered recommendations based on {isSoloSquad() ? "your" : "squad"} preferences
            </CardDescription>
          </div>
          {suggestionsLoaded && (
            <Button
              variant="outline"
              size="icon"
              onClick={handleRefresh}
              disabled={loading || refreshing}
              className="h-8 w-8"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`} />
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[1, 2, 3].map((_, index) => (
                <div key={index} className="rounded-lg border overflow-hidden">
                  <Skeleton className="aspect-video w-full" />
                  <div className="p-3 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <div className="flex gap-2">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                    <Skeleton className="h-10 w-full" />
                    <div className="flex justify-between items-center pt-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-8 w-16" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : error && suggestions.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={handleRefresh} variant="outline">
                Try Again
              </Button>
            </div>
          ) : suggestions.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {suggestions.map((idea, index) => (
                <div key={index} className="rounded-lg border overflow-hidden">
                  <div className="space-y-1">
                    <div className="relative">
                      <OptimizedImage
                        src={idea.image || "/placeholder.svg?height=150&width=300"}
                        alt={idea.destination}
                        aspectRatio="video"
                        className="rounded-t-lg"
                        fallbackSrc="/placeholder.svg?height=150&width=300"
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-3">
                        <h3 className="text-white font-bold">{idea.destination}</h3>
                      </div>
                    </div>
                    {idea.attribution && (
                      <div className="text-[10px] text-muted-foreground text-right leading-tight px-1">
                        Photo:{" "}
                        <a
                          href={idea.attribution.link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          {idea.attribution.name}
                        </a>
                      </div>
                    )}
                  </div>
                  <div className="p-3 space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {idea.tags.map((tag, i) => (
                        <Badge key={i} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground line-clamp-2">{idea.description}</p>
                    <div className="flex justify-between items-center pt-2">
                      <span className="text-sm font-medium">{idea.budget}</span>
                      <Button variant="outline" size="sm" onClick={() => handleViewDetails(idea)}>
                        <Eye className="h-4 w-4 mr-2" /> View
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground mb-6">
                Get AI-powered trip suggestions based on your preferences
              </p>
              <Button
                onClick={() => {
                  fetchSuggestions()
                  setSuggestionsLoaded(true)
                }}
                disabled={loading}
              >
                {loading ? "Loading..." : "Get AI Trip Ideas"}
              </Button>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Link href="/trips/create" className="w-full">
            <Button variant="outline" className="w-full">
              <Plus className="h-4 w-4 mr-2" /> Plan a New Trip
            </Button>
          </Link>
        </CardFooter>
      </Card>
    </>
  )
}
