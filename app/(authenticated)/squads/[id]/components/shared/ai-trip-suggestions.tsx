"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON> } from "next/navigation"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeSquad } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAIUsage } from "@/lib/domains/user-ai-usage/user-ai-usage.realtime.hooks"
import { AIUsageCategory } from "@/lib/domains/user-ai-usage/user-ai-usage.types"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription/user-subscription.hooks"
import { SubscriptionErrorType } from "@/lib/subscription-errors"
import { AIUsageWarning } from "@/components/ai-usage-warning"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Refresh<PERSON><PERSON>, Eye, Sparkles } from "lucide-react"
import { TripSuggestionDetails } from "./trip-suggestion-details"
import { OptimizedImage } from "@/components/optimized-image"
import { useAITripSuggestions } from "@/lib/domains/ai-suggestions/ai-suggestions-trips.hooks"
import { CachedTripSuggestion } from "@/lib/domains/ai-suggestions/ai-suggestions-cache.service"

interface AiTripSuggestionsProps {
  squadId?: string
  onError?: (error: Error) => void
}

export function AiTripSuggestions({ squadId }: AiTripSuggestionsProps) {
  const params = useParams()
  const id = squadId || (params.id as string)
  const currentUser = useUser()
  const isSubscribed = useIsUserSubscribed()
  const { getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)

  // Get real-time squad data
  const { squad } = useRealtimeSquad(id)

  // Use the new AI trip suggestions hook
  const {
    suggestions,
    loading,
    error,
    usageError,
    suggestionsLoaded,
    usingCachedSuggestions,
    loadSuggestions,
  } = useAITripSuggestions(squadId)

  // State for squad members
  const [squadMembers, setSquadMembers] = useState<string[]>([])
  const [refreshing, setRefreshing] = useState(false)

  // State for suggestion details modal
  const [selectedSuggestion, setSelectedSuggestion] = useState<CachedTripSuggestion | null>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)

  // Handle refresh button click
  async function handleRefresh(): Promise<void> {
    setRefreshing(true)
    await loadSuggestions(true)
    setRefreshing(false)
  }

  // Wrapper function for button click events
  const handleFetchClick = () => {
    loadSuggestions(false)
  }

  // Function to check if we're in a solo squad (just the current user)
  const isSoloSquad = (): boolean => {
    return !!(squadMembers.length === 1 && currentUser && squadMembers.includes(currentUser.uid))
  }

  const handleViewDetails = (suggestion: CachedTripSuggestion) => {
    setSelectedSuggestion(suggestion)
    setDetailsOpen(true)
  }

  // Update squad members when squad data changes
  useEffect(() => {
    if (squad && squad.members) {
      setSquadMembers(squad.members)
    }
  }, [squad])

  return (
    <>
      {selectedSuggestion && (
        <TripSuggestionDetails
          suggestion={selectedSuggestion}
          open={detailsOpen}
          onOpenChange={setDetailsOpen}
          squadId={id}
        />
      )}
      {usageError && usageError.show && (
        <AIUsageWarning
          errorType={usageError.errorType}
          usageData={usageError.usageData}
          onClose={() => {}}
          className="mb-4"
        />
      )}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Trip Ideas</CardTitle>
            <CardDescription>
              {usingCachedSuggestions
                ? "Cached trip recommendations based on "
                : "AI-powered recommendations based on "}
              {isSoloSquad() ? "your" : "squad"} preferences
            </CardDescription>
          </div>
          {suggestionsLoaded && (
            <div className="flex items-center gap-2">
              {!isSubscribed && (
                <span className="text-xs text-muted-foreground">
                  {getCategoryUsage(AIUsageCategory.TRIP)?.count || 0}/
                  {getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                </span>
              )}
              {usingCachedSuggestions && (
                <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md">
                  Cached
                </span>
              )}
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleRefresh}
                disabled={loading || refreshing}
              >
                <RefreshCw className={`h-4 w-4 ${loading || refreshing ? "animate-spin" : ""}`} />
              </Button>
            </div>
          )}
        </CardHeader>
        <CardContent>
          {!suggestionsLoaded && !loading && !error && (
            <div className="flex flex-col items-center justify-center py-8">
              <Button onClick={handleFetchClick} disabled={loading}>
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span>Loading...</span>
                  </div>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    {!isSubscribed && (
                      <span className="mr-2 text-xs">
                        {getCategoryUsage(AIUsageCategory.TRIP)?.count || 0}/
                        {getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                      </span>
                    )}
                    {currentUser?.uid && suggestions.length > 0
                      ? "Show Cached Trip Ideas"
                      : "Generate Trip Ideas"}
                  </>
                )}
              </Button>
            </div>
          )}

          {loading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <div className="aspect-video relative">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="pt-4">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <div className="flex gap-2 mb-4">
                      <Skeleton className="h-5 w-16" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-4 w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-500 mb-4">{error}</p>
              <Button onClick={handleFetchClick} disabled={loading}>
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span>Loading...</span>
                  </div>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    {!isSubscribed && (
                      <span className="mr-2 text-xs">
                        {getCategoryUsage(AIUsageCategory.TRIP)?.count || 0}/
                        {getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                      </span>
                    )}
                    Try Again
                  </>
                )}
              </Button>
            </div>
          )}

          {suggestionsLoaded && !loading && !error && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {suggestions.length > 0 ? (
                suggestions.map((suggestion, index) => (
                  <Card key={index} className="overflow-hidden">
                    <div className="aspect-video relative">
                      <OptimizedImage
                        src={suggestion.image}
                        alt={suggestion.destination}
                        aspectRatio="video"
                        className="object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                      <div className="absolute bottom-0 left-0 right-0 p-3">
                        <h3 className="font-semibold text-white">{suggestion.destination}</h3>
                        <p className="text-xs text-white/80">{suggestion.budget}</p>
                      </div>
                    </div>
                    <CardContent className="pt-4">
                      <div className="flex flex-wrap gap-2 mb-3">
                        {suggestion.tags.slice(0, 3).map((tag, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-3">
                        {suggestion.description}
                      </p>
                      <div className="mt-3 flex justify-between items-center">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          onClick={() => handleViewDetails(suggestion)}
                        >
                          <Eye className="h-3 w-3 mr-1" /> View Details
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-8">
                  <p className="text-muted-foreground mb-4">No trip suggestions available</p>
                  <Button onClick={handleFetchClick} disabled={loading}>
                    {loading ? (
                      <div className="flex items-center justify-center gap-2">
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        <span>Loading...</span>
                      </div>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4 mr-2" />
                        {!isSubscribed && (
                          <span className="mr-2 text-xs">
                            {getCategoryUsage(AIUsageCategory.TRIP)?.count || 0}/
                            {getCategoryUsage(AIUsageCategory.TRIP)?.limit || 3}
                          </span>
                        )}
                        Generate Trip Ideas
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </>
  )
}
