"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, UserPlus } from "lucide-react"
import { type Squad, type User } from "@/lib/firebase-service"
import { UserDisplay } from "@/components/user-display"
import { SubscriberBadge } from "@/components/subscriber-badge"

interface MemberWithSubscription extends User {
  isSubscribed?: boolean
}

interface MembersTabProps {
  squad: Squad
  members: MemberWithSubscription[]
  onInviteClick: () => void
  isSquadLead?: boolean
}

export function MembersTab({
  squad,
  members,
  onInviteClick,
  isSquadLead = false,
}: MembersTabProps) {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Squad Members</h2>
        {isSquadLead && (
          <Button onClick={onInviteClick}>
            <UserPlus className="mr-2 h-4 w-4" /> Invite
          </Button>
        )}
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="divide-y">
            {members.map((member, index) => (
              <div key={member.uid || index} className="flex items-center justify-between p-4">
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <UserDisplay
                      displayName={member.displayName}
                      photoURL={member.photoURL}
                      showBadge={false} /* We'll show the badge separately */
                    />
                    {member.isSubscribed && (
                      <div className="flex items-center bg-amber-50 dark:bg-amber-950 px-2 py-0.5 rounded-full">
                        <SubscriberBadge />
                        <span className="text-xs ml-1 text-amber-600 dark:text-amber-400 font-medium">
                          Pro
                        </span>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground ml-10">{member.email}</p>
                </div>
                <div className="flex items-center gap-2">
                  {member.uid === squad.leaderId && <Badge variant="outline">Squad Lead</Badge>}
                  <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
