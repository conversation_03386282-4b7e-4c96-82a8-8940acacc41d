"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Copy, Mail, RefreshCw, Trash2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
  type Squad,
  type User,
  type Invitation,
  getSquadInvitations,
  cancelInvitation,
  resendInvitation,
  canResendInvitation,
} from "@/lib/firebase-service"
import { generateInvitationLink, sendInvitationEmail } from "@/lib/email-service"
import { format, formatDistanceToNow } from "date-fns"

interface InvitationsTabProps {
  squad: Squad
  currentUser: User
  onInviteClick: () => void
  initialInvitations?: Invitation[]
  onInvitationsChange?: (invitations: Invitation[]) => void
}

export function InvitationsTab({
  squad,
  currentUser,
  onInviteClick,
  initialInvitations,
  onInvitationsChange,
}: InvitationsTabProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [activeTab, setActiveTab] = useState("all")
  const [processingIds, setProcessingIds] = useState<string[]>([])

  const isSquadLead = squad.leaderId === currentUser.uid

  useEffect(() => {
    // If initialInvitations is provided, use it (even if it's an empty array)
    if (initialInvitations !== undefined) {
      setInvitations(initialInvitations)
      setLoading(false)
      return
    }

    const fetchInvitations = async () => {
      if (!squad) {
        setLoading(false) // Make sure to set loading to false if there's no squad
        return
      }

      try {
        setLoading(true)
        const invitationsData = await getSquadInvitations(squad.id)
        setInvitations(invitationsData)

        // Notify parent component about the invitations
        if (onInvitationsChange) {
          onInvitationsChange(invitationsData)
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load invitations. Please try again.",
          variant: "destructive",
        })
        // Set invitations to empty array on error to avoid showing loading indefinitely
        setInvitations([])
      } finally {
        setLoading(false)
      }
    }

    fetchInvitations()
  }, [squad, toast, initialInvitations, onInvitationsChange])

  const handleCancelInvitation = async (invitationId: string) => {
    if (!isSquadLead) return

    try {
      setProcessingIds((prev) => [...prev, invitationId])

      await cancelInvitation(invitationId)

      toast({
        title: "Invitation cancelled",
        description: "The invitation has been cancelled successfully.",
      })

      // Remove the invitation from the list
      const updatedInvitations = invitations.filter((inv) => inv.id !== invitationId)
      setInvitations(updatedInvitations)

      // Notify parent component about the change
      if (onInvitationsChange) {
        onInvitationsChange(updatedInvitations)
      }
    } catch (error) {
      console.error("Error cancelling invitation:", error)
      toast({
        title: "Error",
        description: "Failed to cancel invitation. Please try again.",
        variant: "destructive",
      })
    } finally {
      setProcessingIds((prev) => prev.filter((id) => id !== invitationId))
    }
  }

  const handleResendInvitation = async (invitationId: string) => {
    if (!isSquadLead) return

    try {
      setProcessingIds((prev) => [...prev, invitationId])

      // Check if the invitation can be resent
      const canResend = await canResendInvitation(invitationId)

      if (!canResend.canResend) {
        toast({
          title: "Cannot resend invitation",
          description: canResend.error || "This invitation cannot be resent at this time.",
          variant: "destructive",
        })

        if (canResend.nextResendDate) {
          toast({
            title: "Next resend available",
            description: `You can resend this invitation on ${format(canResend.nextResendDate, "PPP")}`,
          })
        }

        setProcessingIds((prev) => prev.filter((id) => id !== invitationId))
        return
      }

      // Resend the invitation
      const result = await resendInvitation(invitationId)

      if (result.success) {
        // Get the updated invitation
        const updatedInvitation = invitations.find((inv) => inv.id === invitationId)

        if (updatedInvitation) {
          // Update the invitation in the list
          const updatedInvitations = invitations.map((inv) =>
            inv.id === invitationId
              ? {
                  ...inv,
                  status: "pending" as "pending",
                  lastResent: { toDate: () => new Date() } as any,
                }
              : inv
          )
          setInvitations(updatedInvitations)

          // Notify parent component about the change
          if (onInvitationsChange) {
            onInvitationsChange(updatedInvitations)
          }

          // Send the invitation email
          // You can specify a template ID here if needed
          await sendInvitationEmail(updatedInvitation, generateInvitationLink(invitationId))

          toast({
            title: "Invitation resent",
            description: "The invitation has been resent successfully.",
          })
        }
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to resend invitation. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error resending invitation:", error)
      toast({
        title: "Error",
        description: "Failed to resend invitation. Please try again.",
        variant: "destructive",
      })
    } finally {
      setProcessingIds((prev) => prev.filter((id) => id !== invitationId))
    }
  }

  const handleCopyLink = (invitationId: string) => {
    const link = generateInvitationLink(invitationId)
    navigator.clipboard.writeText(link)

    toast({
      title: "Link copied",
      description: "Invitation link copied to clipboard.",
    })
  }

  const handleSendEmail = async (invitation: Invitation) => {
    try {
      setProcessingIds((prev) => [...prev, invitation.id])

      // You can specify a template ID here if needed
      const result = await sendInvitationEmail(invitation, generateInvitationLink(invitation.id))

      if (result.success) {
        // Update the invitation in the list to show it was just resent
        const updatedInvitations = invitations.map((inv) =>
          inv.id === invitation.id
            ? { ...inv, lastResent: { toDate: () => new Date() } as any }
            : inv
        ) as Invitation[]
        setInvitations(updatedInvitations)

        // Notify parent component about the change
        if (onInvitationsChange) {
          onInvitationsChange(updatedInvitations)
        }

        toast({
          title: "Email sent",
          description: `Invitation email sent to ${invitation.inviteeEmail}`,
        })
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to send invitation email. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error sending invitation email:", error)
      toast({
        title: "Error",
        description: "Failed to send invitation email. Please try again.",
        variant: "destructive",
      })
    } finally {
      setProcessingIds((prev) => prev.filter((id) => id !== invitation.id))
    }
  }

  // Filter invitations based on active tab
  const filteredInvitations = invitations.filter((invitation) => {
    if (activeTab === "all") return true
    if (activeTab === "pending") return invitation.status === "pending"
    if (activeTab === "accepted") return invitation.status === "accepted"
    if (activeTab === "rejected") return invitation.status === "rejected"
    return true
  })

  if (!isSquadLead) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Squad Invitations</h2>
        </div>

        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">Only the squad leader can manage invitations.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Squad Invitations</h2>
        <Button onClick={onInviteClick}>
          <Mail className="mr-2 h-4 w-4" /> Invite New Member
        </Button>
      </div>

      <Tabs defaultValue="all" className="space-y-4" onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="accepted">Accepted</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab}>
          {loading ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">Loading invitations...</p>
              </CardContent>
            </Card>
          ) : filteredInvitations.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">
                  No {activeTab !== "all" ? activeTab : ""} invitations found.
                </p>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-0">
                <div className="divide-y">
                  {filteredInvitations.map((invitation) => (
                    <div key={invitation.id} className="p-4">
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                        <div>
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{invitation.inviteeEmail}</p>
                            <Badge
                              variant={
                                invitation.status === "pending"
                                  ? "outline"
                                  : invitation.status === "accepted"
                                    ? "default"
                                    : "destructive"
                              }
                              className="capitalize"
                            >
                              {invitation.status}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Invited{" "}
                            {invitation.createdAt
                              ? formatDistanceToNow(invitation.createdAt.toDate(), {
                                  addSuffix: true,
                                })
                              : "recently"}
                            {invitation.lastResent && (
                              <>
                                {" "}
                                • Resent{" "}
                                {formatDistanceToNow(invitation.lastResent.toDate(), {
                                  addSuffix: true,
                                })}
                              </>
                            )}
                          </p>
                        </div>

                        <div className="flex flex-wrap gap-2">
                          {/* Invitation link - only show for pending and rejected invitations */}
                          {invitation.status !== "accepted" && (
                            <div className="flex-1 min-w-[200px]">
                              <Label htmlFor={`link-${invitation.id}`} className="sr-only">
                                Invitation Link
                              </Label>
                              <div className="flex">
                                <Input
                                  id={`link-${invitation.id}`}
                                  value={generateInvitationLink(invitation.id)}
                                  readOnly
                                  className="rounded-r-none"
                                />
                                <Button
                                  variant="secondary"
                                  size="icon"
                                  className="rounded-l-none"
                                  onClick={() => handleCopyLink(invitation.id)}
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          )}

                          {/* Action buttons */}
                          <div className="flex gap-2">
                            {
                              /* Send email button for pending invitations */
                              invitation.status === "pending" && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleSendEmail(invitation)}
                                  disabled={
                                    processingIds.includes(invitation.id) ||
                                    (invitation.lastResent
                                      ? new Date().getTime() -
                                          invitation.lastResent.toDate().getTime() <
                                        5 * 60 * 1000
                                      : false)
                                  }
                                >
                                  <Mail className="h-4 w-4 mr-2" /> Resend
                                </Button>
                              )
                            }
                            {
                              /* Send email button for rejected invitations */
                              invitation.status === "rejected" && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleSendEmail(invitation)}
                                  disabled={processingIds.includes(invitation.id)}
                                >
                                  <Mail className="h-4 w-4 mr-2" /> Email
                                </Button>
                              )
                            }
                            {/* Cancel button (for pending invitations) */}
                            {invitation.status === "pending" && (
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleCancelInvitation(invitation.id)}
                                disabled={processingIds.includes(invitation.id)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" /> Cancel
                              </Button>
                            )}

                            {/* Resend button (for rejected invitations) */}
                            {invitation.status === "rejected" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleResendInvitation(invitation.id)}
                                disabled={processingIds.includes(invitation.id)}
                              >
                                <RefreshCw className="h-4 w-4 mr-2" /> Resend
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
