"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Mail } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { type Squad, type User, type Invitation, inviteUserToSquad } from "@/lib/firebase-service"
import { useToast } from "@/components/ui/use-toast"
import { generateInvitationLink, sendInvitationEmail } from "@/lib/email-service"

interface InviteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  squad: Squad
  currentUser: User
  onInviteSent?: (invitation: Invitation) => void
}

export function InviteDialog({
  open,
  onOpenChange,
  squad,
  currentUser,
  onInviteSent,
}: InviteDialogProps) {
  const { toast } = useToast()
  const [inviteEmail, setInviteEmail] = useState("")
  const [isInviting, setIsInviting] = useState(false)
  const [inviteError, setInviteError] = useState<string | null>(null)

  const handleInvite = async () => {
    if (!inviteEmail.trim() || !squad || !currentUser) return

    try {
      setIsInviting(true)
      setInviteError(null)

      const result = await inviteUserToSquad(squad.id, inviteEmail.trim(), {
        uid: currentUser.uid,
        email: currentUser.email || "",
        displayName: currentUser.displayName || "",
        photoURL: currentUser.photoURL || "",
        createdAt: null,
      })

      if (result.success && result.invitationId) {
        // Create invitation object
        const invitation: Invitation = {
          id: result.invitationId,
          squadId: squad.id,
          squadName: squad.name,
          inviterId: currentUser.uid,
          inviterName: currentUser.displayName || currentUser.email || "",
          inviteeId: result.user?.uid || "", // May be empty for non-existing users
          inviteeEmail: inviteEmail.trim(), // Always use the email that was entered
          status: "pending",
          createdAt: { toDate: () => new Date() } as any,
        }

        // Send invitation email
        // You can specify a template ID here if needed
        await sendInvitationEmail(invitation, generateInvitationLink(result.invitationId))

        // Notify parent component about the new invitation
        if (onInviteSent) {
          onInviteSent(invitation)
        }

        toast({
          title: "Invitation sent",
          description: `An invitation has been sent to ${inviteEmail}`,
        })

        setInviteEmail("")
        onOpenChange(false)
      } else {
        setInviteError(result.error || "Failed to invite user. Please try again.")
      }
    } catch (error) {
      console.error("Error inviting user:", error)
      setInviteError("An unexpected error occurred. Please try again.")
    } finally {
      setIsInviting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Invite to {squad.name}</DialogTitle>
          <DialogDescription>
            Enter the email address of the person you want to invite to this squad.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                placeholder="<EMAIL>"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
            </div>
            {inviteError && <p className="text-sm text-destructive mt-2">{inviteError}</p>}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleInvite} disabled={isInviting || !inviteEmail.trim()}>
            {isInviting ? "Inviting..." : "Send Invite"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
