"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

import { MapPin, Star } from "lucide-react"
import { <PERSON> } from "@/lib/domains/trip/trip.types"
import { SkeletonStats } from "@/components/skeleton-loader"

interface SquadStatsProps {
  upcomingTrips: Trip[]
  pastTrips: Trip[]
}

export function SquadStats({ upcomingTrips, pastTrips }: SquadStatsProps) {
  const [loading, setLoading] = useState(true)
  const [mostPopularDestination, setMostPopularDestination] = useState<string>("")
  // Default rating is 4 out of 5
  const averageRating = 4

  // Calculate most popular destination
  useEffect(() => {
    if (pastTrips.length === 0) {
      setLoading(false)
      return
    }

    // This is a simplified implementation - in a real app, you would count occurrences
    // of each destination and find the most frequent one
    try {
      // Simulate some data processing
      setTimeout(() => {
        if (pastTrips.length > 0) {
          setMostPopularDestination(pastTrips[0].destination)
        }
        setLoading(false)
      }, 300)
    } catch (error) {
      console.error("Error calculating stats:", error)
      setLoading(false)
    }
  }, [pastTrips])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Squad Stats</CardTitle>
        <CardDescription>Trip history and activity</CardDescription>
      </CardHeader>
      {loading ? (
        <CardContent>
          <SkeletonStats />
        </CardContent>
      ) : (
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Total Trips</h4>
              <span className="font-bold">{upcomingTrips.length + pastTrips.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Upcoming Trips</h4>
              <span className="font-bold">{upcomingTrips.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Past Trips</h4>
              <span className="font-bold">{pastTrips.length}</span>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="text-sm font-medium">Average Trip Rating</h4>
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-5 w-5 ${i < averageRating ? "text-yellow-400 fill-yellow-400" : "text-muted-foreground"}`}
                />
              ))}
            </div>
          </div>

          {pastTrips.length > 0 && mostPopularDestination && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Most Popular Destination</h4>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-primary" />
                <span>{mostPopularDestination}</span>
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  )
}
