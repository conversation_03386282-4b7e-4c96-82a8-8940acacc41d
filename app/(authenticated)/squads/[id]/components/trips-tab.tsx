"use client"

import Link from "next/link"
import { useParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, Clock, Compass, Plus, Star, Users } from "lucide-react"
import { Trip } from "@/lib/domains/trip/trip.types"
import { TripCard } from "./trip-card"
import { TripIdeasSection } from "./trip-ideas-section"

interface TripsTabProps {
  upcomingTrips: Trip[]
  pastTrips: Trip[]
}

export function TripsTab({ upcomingTrips, pastTrips }: TripsTabProps) {
  const params = useParams()
  const squadId = params.id as string

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">All Trips</h2>
        <Link href={`/squads/${squadId}/create-trip`}>
          <Button>
            <Plus className="mr-2 h-4 w-4" /> Plan a Trip
          </Button>
        </Link>
      </div>

      <Tabs defaultValue="upcoming">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="past">Past</TabsTrigger>
          <TabsTrigger value="ideas">Ideas</TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="mt-4">
          {upcomingTrips.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {upcomingTrips.map((trip) => (
                <TripCard key={trip.id} trip={trip} isPast={false} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <Compass className="h-6 w-6 text-primary" />
                </div>
                <p className="font-medium text-center">No Upcoming Trips</p>
                <p className="text-sm text-muted-foreground text-center mt-1 mb-4">
                  Plan your next adventure with this squad
                </p>
                <Link href={`/squads/${squadId}/create-trip`}>
                  <Button>Plan a Trip</Button>
                </Link>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="past" className="mt-4">
          {pastTrips.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {pastTrips.map((trip) => (
                <TripCard key={trip.id} trip={trip} isPast={true} />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <Calendar className="h-6 w-6 text-primary" />
                </div>
                <p className="font-medium text-center">No Past Trips</p>
                <p className="text-sm text-muted-foreground text-center mt-1">
                  Your completed trips will appear here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="ideas" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Reuse the trip ideas component */}
            <TripIdeasSection />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
