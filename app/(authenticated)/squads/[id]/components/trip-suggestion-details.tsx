"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useUserWithData } from "@/lib/domains/auth/auth.hooks"
import { useUserSubscriptionWithInit } from "@/lib/domains/user-subscription/user-subscription.hooks"
import { useUserAIUsage } from "@/lib/domains/user-ai-usage/user-ai-usage.hooks"
import { useUserPreferences } from "@/lib/domains/user-preferences/user-preferences.hooks"
import { SubscriptionErrorType } from "@/lib/subscription-errors"
import { AIUsageWarning } from "@/components/ai-usage-warning"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Skeleton } from "@/components/ui/skeleton"
import { Clock, Loader2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { generateDestinationActivities, type ActivitySuggestion } from "@/lib/openai"
import { createTrip, getUserSquads, type Squad } from "@/lib/firebase-service"
import { format, addDays } from "date-fns"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { CalendarIcon } from "lucide-react"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { getLocationImage, getLocationImageByPlaceId } from "@/lib/google-places"
import { OptimizedImage } from "@/components/optimized-image"

interface TripSuggestion {
  destination: string
  image: string
  tags: string[]
  description: string
  budget: string
  placeId?: string
  formattedAddress?: string
  attribution?: {
    name: string
    photoReference?: string
    username?: string
    link?: string
  }
}

interface TripSuggestionDetailsProps {
  suggestion: TripSuggestion | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface TripFormData {
  name: string
  destination: string
  squadId: string
  startDate: Date | null
  endDate: Date | null
  budget: string
  description: string
}

export function TripSuggestionDetails({
  suggestion,
  open,
  onOpenChange,
}: TripSuggestionDetailsProps) {
  const router = useRouter()
  const { user } = useUserWithData()
  const { isSubscribed, canCreateMoreTripsInSquad, handleSubscriptionError } =
    useUserSubscriptionWithInit()

  const { incrementUsage, canMakeRequest, usage } = useUserAIUsage(isSubscribed)
  const [usageWarning, setUsageWarning] = useState<{
    show: boolean
    errorType:
      | SubscriptionErrorType.DAILY_AI_LIMIT_REACHED
      | SubscriptionErrorType.WEEKLY_AI_LIMIT_REACHED
    usageData: {
      daily: number
      weekly: number
      dailyLimit: number
      weeklyLimit: number
    }
  } | null>(null)
  const [activeTab, setActiveTab] = useState("activities")
  const [activities, setActivities] = useState<ActivitySuggestion[]>([])
  const [loadingActivities, setLoadingActivities] = useState(true)
  const [squads, setSquads] = useState<Squad[]>([])
  const [loadingSquads, setLoadingSquads] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [locationImage, setLocationImage] = useState<string | null>(null)
  const [imageAttribution, setImageAttribution] = useState<{
    name: string
    photoReference?: string
    username?: string
    link?: string
  } | null>(null)
  const [loadingImage, setLoadingImage] = useState(false)

  // Get user travel preferences from domain hook
  const { preferences: userPreferences } = useUserPreferences()
  const storeTravelTypes = userPreferences?.travelPreferences || []

  // Form state for trip creation
  const [formData, setFormData] = useState<TripFormData>({
    name: "",
    destination: "",
    squadId: "",
    startDate: null,
    endDate: null,
    budget: "",
    description: "",
  })

  // Fetch activities when suggestion changes
  useEffect(() => {
    if (!suggestion || !open) return

    const fetchActivities = async () => {
      try {
        setLoadingActivities(true)

        // Check AI usage before making a request
        const canMakeAIRequest = await canMakeRequest()

        // If we can't make a request, show appropriate warning
        if (!canMakeAIRequest && usage) {
          const usageData = {
            daily: usage.daily,
            weekly: usage.weekly,
            dailyLimit: usage.dailyLimit,
            weeklyLimit: usage.weeklyLimit,
          }

          // Determine if it's a daily or weekly limit that was reached
          if (usage.daily >= usage.dailyLimit) {
            setUsageWarning({
              show: true,
              errorType: SubscriptionErrorType.DAILY_AI_LIMIT_REACHED,
              usageData,
            })
          } else if (usage.weekly >= usage.weeklyLimit) {
            setUsageWarning({
              show: true,
              errorType: SubscriptionErrorType.WEEKLY_AI_LIMIT_REACHED,
              usageData,
            })
          }

          // Set default activities
          setActivities([
            {
              title: `Visit popular attractions in ${suggestion.destination}`,
              description: `Explore the most famous sights and landmarks in ${suggestion.destination}.`,
              cost: "$20 - $50 per person",
              duration: "3-4 hours",
            },
            {
              title: `Try local cuisine`,
              description: `Sample the delicious local food specialties in ${suggestion.destination}.`,
              cost: "$30 - $60 per person",
              duration: "1-2 hours",
            },
            {
              title: `Explore nature`,
              description: `Discover the natural beauty surrounding ${suggestion.destination}.`,
              cost: "$0 - $30 per person",
              duration: "2-5 hours",
            },
          ])
          setLoadingActivities(false)
          return
        }

        // Extract budget range from suggestion
        const budgetStr = suggestion.budget

        // Get user preferences from the user preferences domain
        const preferences = userPreferences?.travelPreferences || storeTravelTypes || []

        // Fetch activity suggestions
        const activitySuggestions = await generateDestinationActivities(
          suggestion.destination,
          budgetStr,
          preferences
        )

        setActivities(activitySuggestions)

        // Only increment the user's AI usage counter after successful generation
        await incrementUsage()
      } catch (error) {
        console.error("Error fetching activities:", error)
        toast({
          title: "Error",
          description: "Failed to load activity suggestions. Please try again.",
          variant: "destructive",
        })

        // Set default activities
        setActivities([
          {
            title: `Visit popular attractions in ${suggestion.destination}`,
            description: `Explore the most famous sights and landmarks in ${suggestion.destination}.`,
            cost: "$20 - $50 per person",
            duration: "3-4 hours",
          },
          {
            title: `Try local cuisine`,
            description: `Sample the delicious local food specialties in ${suggestion.destination}.`,
            cost: "$30 - $60 per person",
            duration: "1-2 hours",
          },
          {
            title: `Explore nature`,
            description: `Discover the natural beauty surrounding ${suggestion.destination}.`,
            cost: "$0 - $30 per person",
            duration: "2-5 hours",
          },
        ])
      } finally {
        setLoadingActivities(false)
      }
    }

    const fetchLocationImage = async () => {
      if (!suggestion) return

      try {
        setLoadingImage(true)

        // If we have a place ID, use it to get the image
        if (suggestion.placeId) {
          const imageResult = await getLocationImageByPlaceId(
            suggestion.placeId,
            suggestion.destination
          )
          setLocationImage(imageResult.url)
          if (imageResult.attribution) {
            setImageAttribution(imageResult.attribution)
          }
        }
        // Otherwise, fall back to text search
        else if (suggestion.destination) {
          const imageResult = await getLocationImage(suggestion.destination)
          setLocationImage(imageResult.url)
          if (imageResult.attribution) {
            setImageAttribution(imageResult.attribution)
          }
        }
      } catch (error) {
        console.error("Error fetching location image:", error)
        // Use the existing image or placeholder
        setLocationImage(suggestion.image || "/placeholder.svg?height=300&width=600")
        setImageAttribution(null)
      } finally {
        setLoadingImage(false)
      }
    }

    // Fetch user's squads
    const fetchSquads = async () => {
      if (!user) return

      try {
        setLoadingSquads(true)
        const squadsList = await getUserSquads(user.uid)
        setSquads(squadsList || [])
      } catch (error) {
        console.error("Error fetching squads:", error)
        toast({
          title: "Error",
          description: "Failed to load squads. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoadingSquads(false)
      }
    }

    // Initialize form data with suggestion
    if (suggestion) {
      // Extract budget from suggestion and convert to a numeric value with $ prefix
      let budgetValue = ""

      // Try to extract a numeric value from various possible formats
      const budgetMatch = suggestion.budget.match(/\$([\d,]+)\s*(?:-|to)\s*\$([\d,]+)|\$([\d,]+)/)
      if (budgetMatch) {
        // If it's a range, use the maximum value
        if (budgetMatch[2]) {
          // It's a range like "$1000 - $2000" or "$1000 to $2000"
          budgetValue = budgetMatch[2].replace(/,/g, "")
        } else if (budgetMatch[3]) {
          // It's a single value like "$1000"
          budgetValue = budgetMatch[3].replace(/,/g, "")
        } else if (budgetMatch[1]) {
          // Fallback to first capture group
          budgetValue = budgetMatch[1].replace(/,/g, "")
        }
      } else {
        // If no match, try to extract any number from the string
        const numericMatch = suggestion.budget.match(/([\d,]+)/)
        if (numericMatch) {
          budgetValue = numericMatch[1].replace(/,/g, "")
        }
      }

      setFormData({
        name: `Trip to ${suggestion.destination}`,
        destination: suggestion.destination,
        squadId: "",
        startDate: null,
        endDate: null,
        budget: budgetValue ? `${budgetValue}` : "",
        description: suggestion.description,
      })
    }

    fetchActivities()
    fetchLocationImage()
    fetchSquads()
  }, [suggestion, open, userPreferences, storeTravelTypes, user])

  const handleChange = (field: keyof TripFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async () => {
    if (!user) return

    if (
      !formData.name ||
      !formData.destination ||
      !formData.squadId ||
      !formData.startDate ||
      !formData.endDate
    ) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    // Get the selected squad
    const squad = squads.find((s) => s.id === formData.squadId)
    if (!squad) {
      toast({
        title: "Error",
        description: "Selected squad not found.",
        variant: "destructive",
      })
      return
    }

    // Check subscription limits before creating the trip
    if (!isSubscribed && user) {
      const canCreate = await canCreateMoreTripsInSquad(user.uid, formData.squadId)
      if (!canCreate) {
        // Use centralized error handling
        handleSubscriptionError(SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED)
        return
      }
    }

    try {
      setSubmitting(true)

      // Create the trip
      const tripId = await createTrip({
        name: formData.name,
        destination: formData.destination,
        squadId: formData.squadId,
        startDate: formData.startDate as any,
        endDate: formData.endDate as any,
        budget: formData.budget,
        description: formData.description,
        // Only include locationThumbnail if we have a valid image
        ...(locationImage ? { locationThumbnail: locationImage } : {}),
        status: "planning",
        attendees: squad.members,
        leaderId: user.uid,
        createdBy: user.uid,
      })

      toast({
        title: "Trip created!",
        description: "Your trip has been created successfully.",
      })

      // Close the dialog
      onOpenChange(false)

      // Redirect to the trip page
      router.push(`/trips/${tripId}`)
    } catch (error) {
      console.error("Error creating trip:", error)
      toast({
        title: "Error",
        description: "Failed to create trip. Please try again.",
        variant: "destructive",
      })
      setSubmitting(false)
    }
  }

  if (!suggestion) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        {usageWarning && usageWarning.show && (
          <AIUsageWarning
            errorType={usageWarning.errorType}
            usageData={usageWarning.usageData}
            onClose={() => setUsageWarning((prev) => (prev ? { ...prev, show: false } : null))}
            className="mb-4"
          />
        )}
        <DialogHeader>
          <DialogTitle className="text-2xl">{suggestion.destination}</DialogTitle>
          <DialogDescription>{suggestion.description}</DialogDescription>
          <div className="flex flex-wrap gap-2 mt-2">
            {suggestion.tags.map((tag, i) => (
              <Badge key={i} variant="secondary">
                {tag}
              </Badge>
            ))}
          </div>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column: Image and description */}
          <div>
            <div className="space-y-1">
              <div className="rounded-lg overflow-hidden">
                {loadingImage ? (
                  <div className="absolute inset-0 flex items-center justify-center bg-muted z-10">
                    <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  </div>
                ) : null}
                <OptimizedImage
                  src={locationImage || suggestion.image || "/placeholder.svg?height=300&width=600"}
                  alt={suggestion.destination}
                  aspectRatio="video"
                  className="rounded-lg"
                  onLoad={() => setLoadingImage(false)}
                  priority
                  quality={90}
                />
              </div>
              {imageAttribution && (
                <div className="text-xs text-muted-foreground text-right">
                  {imageAttribution.link ? (
                    <>
                      Photo by{" "}
                      <a
                        href={imageAttribution.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline"
                      >
                        {imageAttribution.name}
                      </a>{" "}
                      on{" "}
                      <a
                        href="https://unsplash.com"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:underline"
                      >
                        Unsplash
                      </a>
                    </>
                  ) : (
                    <>Photo of {imageAttribution.name} via Google Places</>
                  )}
                </div>
              )}
            </div>
            <div className="space-y-4">
              <p className="text-sm">{suggestion.description}</p>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Estimated Cost: {suggestion.budget}</span>
              </div>
            </div>
          </div>

          {/* Right column: Tabs for Activities and Create Trip */}
          <div>
            <Tabs defaultValue="activities" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full">
                <TabsTrigger value="activities" className="flex-1">
                  Activities
                </TabsTrigger>
                <TabsTrigger value="create" className="flex-1">
                  Create Trip
                </TabsTrigger>
              </TabsList>

              {/* Activities Tab */}
              <TabsContent value="activities" className="space-y-4 mt-4">
                <h3 className="text-lg font-medium">Suggested Activities</h3>

                {loadingActivities ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map((_, i) => (
                      <Card key={i}>
                        <CardContent className="p-4 space-y-2">
                          <Skeleton className="h-5 w-3/4" />
                          <Skeleton className="h-4 w-full" />
                          <div className="flex justify-between">
                            <Skeleton className="h-4 w-1/4" />
                            <Skeleton className="h-4 w-1/4" />
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {activities.map((activity, i) => (
                      <Card key={i}>
                        <CardContent className="p-4 space-y-2">
                          <h4 className="font-medium">{activity.title}</h4>
                          <p className="text-sm text-muted-foreground">{activity.description}</p>
                          <div className="flex justify-between text-sm">
                            <div className="flex items-center gap-1">
                              <span>Cost: {activity.cost}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3.5 w-3.5" />
                              <span>{activity.duration}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}

                <div className="pt-4">
                  <Button onClick={() => setActiveTab("create")} className="w-full">
                    Create Trip to {suggestion.destination}
                  </Button>
                </div>
              </TabsContent>

              {/* Create Trip Tab */}
              <TabsContent value="create" className="space-y-4 mt-4">
                <h3 className="text-lg font-medium">Create Trip</h3>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Trip Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleChange("name", e.target.value)}
                      placeholder="Summer Vacation 2023"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="destination">Destination</Label>
                    <Input
                      id="destination"
                      value={formData.destination}
                      onChange={(e) => handleChange("destination", e.target.value)}
                      placeholder="Miami, Florida"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="squad">Squad</Label>
                    {loadingSquads ? (
                      <Skeleton className="h-10 w-full" />
                    ) : (
                      <select
                        id="squad"
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        value={formData.squadId}
                        onChange={(e) => handleChange("squadId", e.target.value)}
                        required
                      >
                        <option value="" disabled>
                          Select a squad
                        </option>
                        {squads.map((squad) => (
                          <option key={squad.id} value={squad.id}>
                            {squad.name}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="startDate">Start Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id="startDate"
                            variant={"outline"}
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !formData.startDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {formData.startDate ? (
                              format(formData.startDate, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <CalendarComponent
                            mode="single"
                            selected={formData.startDate || undefined}
                            onSelect={(date) => {
                              handleChange("startDate", date)
                              // Set end date to 7 days after start date by default
                              if (date) {
                                handleChange("endDate", addDays(date, 7))
                              }
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="endDate">End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id="endDate"
                            variant={"outline"}
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !formData.endDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {formData.endDate ? (
                              format(formData.endDate, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <CalendarComponent
                            mode="single"
                            selected={formData.endDate || undefined}
                            onSelect={(date) => handleChange("endDate", date)}
                            disabled={(date) =>
                              formData.startDate ? date < formData.startDate : false
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="budget">Budget (USD)</Label>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                        $
                      </span>
                      <Input
                        id="budget"
                        value={
                          formData.budget.startsWith("$")
                            ? formData.budget.substring(1)
                            : formData.budget
                        }
                        onChange={(e) => {
                          // Only allow numeric inputs
                          const value = e.target.value.replace(/[^0-9]/g, "")
                          handleChange("budget", `${value}`)
                        }}
                        className="pl-6"
                        placeholder="1000"
                        type="number"
                        min="0"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleChange("description", e.target.value)}
                      placeholder="Add some details about this trip..."
                      rows={3}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <DialogFooter>
          {activeTab === "create" && (
            <Button onClick={handleSubmit} disabled={submitting} className="w-full sm:w-auto">
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Trip
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
