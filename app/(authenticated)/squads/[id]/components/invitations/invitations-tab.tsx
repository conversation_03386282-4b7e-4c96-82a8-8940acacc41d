"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UserPlus } from "lucide-react"
import { useRealtimeSquadInvitations } from "@/lib/domains/invitation/invitation.realtime.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { Squad } from "@/lib/domains/squad/squad.types"
import { InvitationList } from "./invitation-list"
import { PageLoading } from "@/components/page-loading"
import { toast } from "@/components/ui/use-toast"
import { ErrorBoundary } from "@/components/error-boundary"
import { Card, CardContent } from "@/components/ui/card"

interface InvitationsTabProps {
  squad: Squad
  onInviteClick?: () => void
}

export function InvitationsTab({ squad, onInviteClick }: InvitationsTabProps) {
  const squadId = squad.id
  const {
    invitations,
    loading: invitationsLoading,
    error: invitationsError,
  } = useRealtimeSquadInvitations(squadId)
  const user = useUser()

  const [isSquadLead, setIsSquadLead] = useState(false)

  // Show error toast if there's an error
  useEffect(() => {
    if (invitationsError) {
      toast({
        title: "Error",
        description: "Unable to load invitations. Please try again later.",
        variant: "destructive",
      })
    }
  }, [invitationsError])

  // Check if current user is squad leader
  useEffect(() => {
    if (user) {
      setIsSquadLead(user.uid === squad.leaderId)
    } else {
      setIsSquadLead(false)
    }
  }, [user, squad.leaderId])

  if (invitationsLoading) {
    return <PageLoading />
  }

  if (!isSquadLead) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <h3 className="font-medium mb-2">Access Restricted</h3>
          <p className="text-muted-foreground">
            Only the squad leader can view and manage invitations.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Squad Invitations</h2>
          {onInviteClick && (
            <Button onClick={onInviteClick}>
              <UserPlus className="mr-2 h-4 w-4" /> Invite
            </Button>
          )}
        </div>

        <InvitationList invitations={invitations} squadId={squadId} />
      </div>
    </ErrorBoundary>
  )
}
