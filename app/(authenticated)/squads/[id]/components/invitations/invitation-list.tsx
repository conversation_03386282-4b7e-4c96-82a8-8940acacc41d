"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Copy, Mail, RefreshCw, Trash2, ChevronLeft, ChevronRight } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Invitation } from "@/lib/domains/invitation/invitation.types"
import { InvitationService } from "@/lib/domains/invitation/invitation.service"
import { ErrorBoundary } from "@/components/error-boundary"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useIsMobile } from "@/hooks/use-mobile"

interface InvitationListProps {
  invitations: Invitation[]
  squadId: string
}

export function InvitationList({ invitations, squadId }: InvitationListProps) {
  const [processingInvitations, setProcessingInvitations] = useState<Record<string, boolean>>({})
  const [activeTab, setActiveTab] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const isMobile = useIsMobile()
  const [invitationsPerPage] = useState(isMobile ? 3 : 5)

  // Format date safely
  const formatDate = (timestamp: any) => {
    if (!timestamp) return "Unknown"

    try {
      return typeof timestamp.toDate === "function"
        ? timestamp.toDate().toLocaleString()
        : new Date(timestamp).toLocaleString()
    } catch (error) {
      console.error("Error formatting date:", error)
      return "Invalid date"
    }
  }

  const handleCopyLink = (invitationId: string) => {
    const link = `${window.location.origin}/invitation/${invitationId}`
    navigator.clipboard.writeText(link)
    toast({
      title: "Link copied",
      description: "Invitation link copied to clipboard",
    })
  }

  const handleResendInvitation = async (invitation: Invitation) => {
    if (!invitation.id) return

    try {
      setProcessingInvitations((prev) => ({ ...prev, [invitation.id!]: true }))

      // Resend the invitation
      const result = await InvitationService.resendInvitation(invitation.id)

      if (!result.success) {
        throw new Error("Failed to resend invitation")
      }

      toast({
        title: "Invitation resent",
        description: `Invitation resent to ${invitation.inviteeEmail}`,
      })
    } catch (error) {
      console.error("Error resending invitation:", error)
      toast({
        title: "Error",
        description: "Failed to resend invitation",
        variant: "destructive",
      })
    } finally {
      setProcessingInvitations((prev) => ({ ...prev, [invitation.id!]: false }))
    }
  }

  const handleCancelInvitation = async (invitationId: string) => {
    if (!invitationId) return

    try {
      setProcessingInvitations((prev) => ({ ...prev, [invitationId]: true }))

      // Cancel the invitation
      const result = await InvitationService.deleteInvitation(invitationId)

      if (!result.success) {
        throw new Error("Failed to cancel invitation")
      }

      toast({
        title: "Invitation cancelled",
        description: "The invitation has been cancelled",
      })
    } catch (error) {
      console.error("Error cancelling invitation:", error)
      toast({
        title: "Error",
        description: "Failed to cancel invitation",
        variant: "destructive",
      })
    } finally {
      setProcessingInvitations((prev) => ({ ...prev, [invitationId]: false }))
    }
  }

  // Filter invitations based on active tab
  const filteredInvitations = invitations.filter((invitation) => {
    if (activeTab === "all") return true
    if (activeTab === "pending") return invitation.status === "pending"
    if (activeTab === "accepted") return invitation.status === "accepted"
    if (activeTab === "rejected") return invitation.status === "rejected"
    return true
  })

  // Get current invitations for pagination
  const indexOfLastInvitation = currentPage * invitationsPerPage
  const indexOfFirstInvitation = indexOfLastInvitation - invitationsPerPage
  const currentInvitations = filteredInvitations.slice(
    indexOfFirstInvitation,
    indexOfLastInvitation
  )
  const totalPages = Math.ceil(filteredInvitations.length / invitationsPerPage)

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber)

  if (invitations.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-muted-foreground">No pending invitations</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <ErrorBoundary>
      <div className="space-y-4">
        <Tabs
          defaultValue="all"
          className="space-y-4"
          onValueChange={(value) => {
            setActiveTab(value)
            setCurrentPage(1) // Reset to first page when changing tabs
          }}
        >
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="accepted">Accepted</TabsTrigger>
            <TabsTrigger value="rejected">Rejected</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab}>
            {filteredInvitations.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">
                    No {activeTab !== "all" ? activeTab : ""} invitations found.
                  </p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-0">
                  <div className="divide-y">
                    {currentInvitations.map((invitation) => (
                      <div key={invitation.id} className="p-4">
                        {/* Row 1: Email address with horizontal scrolling */}
                        <div className="overflow-x-auto scrollbar-thin pb-2 -mx-1 px-1">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="font-medium whitespace-nowrap cursor-default">
                                  {invitation.inviteeEmail}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{invitation.inviteeEmail}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>

                        {/* Row 2: Date on left, actions on right for pending invitations */}
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-sm text-muted-foreground">
                            Sent: {formatDate(invitation.createdAt)}
                          </p>

                          {invitation.status === "pending" ? (
                            <div className="flex items-center gap-1 sm:gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => handleCopyLink(invitation.id!)}
                                disabled={processingInvitations[invitation.id!]}
                                title="Copy invitation link"
                                aria-label="Copy invitation link"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => handleResendInvitation(invitation)}
                                disabled={processingInvitations[invitation.id!]}
                                title="Resend invitation email"
                                aria-label="Resend invitation email"
                              >
                                {processingInvitations[invitation.id!] ? (
                                  <RefreshCw className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Mail className="h-4 w-4" />
                                )}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => handleCancelInvitation(invitation.id!)}
                                disabled={processingInvitations[invitation.id!]}
                                title="Cancel invitation"
                                aria-label="Cancel invitation"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ) : (
                            <Badge
                              variant="outline"
                              className={`text-xs ${
                                invitation.status === "accepted"
                                  ? "bg-green-50 text-green-800 hover:bg-green-50/80 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30"
                                  : "bg-red-50 text-red-800 hover:bg-red-50/80 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30"
                              }`}
                            >
                              {invitation.status}
                            </Badge>
                          )}
                        </div>

                        {/* Row 3: Badge for pending invitations only */}
                        {invitation.status === "pending" && (
                          <div className="mt-2">
                            <Badge
                              variant="outline"
                              className="text-xs bg-yellow-50 text-yellow-800 hover:bg-yellow-50/80 dark:bg-yellow-900/20 dark:text-yellow-400 dark:hover:bg-yellow-900/30"
                            >
                              {invitation.status}
                            </Badge>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {totalPages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-4">
            <Button
              variant="outline"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => paginate(currentPage - 1)}
              disabled={currentPage === 1}
              aria-label="Previous page"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm px-1">
              {currentPage}/{totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => paginate(currentPage + 1)}
              disabled={currentPage === totalPages}
              aria-label="Next page"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </ErrorBoundary>
  )
}
