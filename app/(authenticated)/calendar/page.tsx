"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight, Plus, CalendarIcon, Users, MapPin } from "lucide-react"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { auth } from "@/lib/firebase"
import { getUserSquads, getUserTrips, type Squad } from "@/lib/firebase-service"
import { useAuthState } from "react-firebase-hooks/auth"
import { Skeleton } from "@/components/ui/skeleton"

interface CalendarEvent {
  id: string
  title: string
  startDate: Date
  endDate: Date
  squad: string
  squadId: string
  type: "trip" | "event"
  tripId?: string
}

export default function CalendarPage() {
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [selectedSquad, setSelectedSquad] = useState("all")
  const [user, loading] = useAuthState(auth)
  const [squads, setSquads] = useState<Squad[]>([])
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Get month and year for display
  const monthYear = currentMonth.toLocaleDateString("en-US", { month: "long", year: "numeric" })

  // Get days in month
  const daysInMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0).getDate()

  // Get first day of month (0 = Sunday, 1 = Monday, etc.)
  const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1).getDay()

  useEffect(() => {
    const fetchData = async () => {
      if (!user) return

      try {
        setIsLoading(true)

        // Fetch user's squads
        const userSquads = await getUserSquads(user.uid)
        setSquads(userSquads)

        // Fetch user's trips
        const userTrips = await getUserTrips(user.uid)

        // Convert trips to calendar events
        const calendarEvents: CalendarEvent[] = userTrips.map((trip) => {
          const squad = userSquads.find((s) => s.id === trip.squadId)
          return {
            id: trip.id,
            title: trip.destination,
            startDate: trip.startDate.toDate(),
            endDate: trip.endDate.toDate(),
            squad: squad?.name || "Unknown Squad",
            squadId: trip.squadId,
            type: "trip",
            tripId: trip.id,
          }
        })

        setEvents([...calendarEvents])
      } catch (error) {
        console.error("Error fetching calendar data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [user])

  // Previous month
  const prevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1))
  }

  // Next month
  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1))
  }

  // Today
  const goToToday = () => {
    setCurrentMonth(new Date())
  }

  // Filter events by selected squad
  const filteredEvents =
    selectedSquad === "all" ? events : events.filter((event) => event.squadId === selectedSquad)

  // Get events for a specific day
  const getEventsForDay = (day: number) => {
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day)
    return filteredEvents.filter((event) => {
      return (
        date >= new Date(event.startDate.setHours(0, 0, 0, 0)) &&
        date <= new Date(event.endDate.setHours(23, 59, 59, 999))
      )
    })
  }

  // Check if a day is today
  const isToday = (day: number) => {
    const today = new Date()
    return (
      day === today.getDate() &&
      currentMonth.getMonth() === today.getMonth() &&
      currentMonth.getFullYear() === today.getFullYear()
    )
  }

  if (isLoading) {
    return <CalendarSkeleton />
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-3xl font-bold">Calendar</h1>
              <p className="text-muted-foreground">View and manage your squad trips and events</p>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Select value={selectedSquad} onValueChange={setSelectedSquad}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="All Squads" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Squads</SelectItem>
                  {squads.map((squad) => (
                    <SelectItem key={squad.id} value={squad.id}>
                      {squad.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Link href="/trips/create">
                <Button>
                  <Plus className="mr-2 h-4 w-4" /> New Trip
                </Button>
              </Link>
            </div>
          </div>

          <Card className="mb-6">
            <CardContent className="p-0">
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-xl font-semibold">{monthYear}</h2>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="icon" onClick={prevMonth}>
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" onClick={goToToday}>
                    Today
                  </Button>
                  <Button variant="outline" size="icon" onClick={nextMonth}>
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-7 text-center py-2 border-b bg-muted/50">
                {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day, index) => (
                  <div key={index} className="text-sm font-medium">
                    {day}
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-7 auto-rows-fr">
                {/* Empty cells for days before the first day of the month */}
                {Array.from({ length: firstDayOfMonth }).map((_, index) => (
                  <div
                    key={`empty-${index}`}
                    className="border-t border-r min-h-[120px] p-1 bg-muted/20"
                  ></div>
                ))}

                {/* Days of the month */}
                {Array.from({ length: daysInMonth }).map((_, index) => {
                  const day = index + 1
                  const dayEvents = getEventsForDay(day)

                  return (
                    <div
                      key={day}
                      className={`border-t border-r min-h-[120px] p-1 ${isToday(day) ? "bg-primary/5" : ""}`}
                    >
                      <div className="flex justify-between items-start">
                        <span
                          className={`text-sm font-medium h-6 w-6 flex items-center justify-center rounded-full ${isToday(day) ? "bg-primary text-primary-foreground" : ""}`}
                        >
                          {day}
                        </span>
                        {dayEvents.length > 0 && (
                          <span className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded-full">
                            {dayEvents.length}
                          </span>
                        )}
                      </div>

                      <div className="mt-1 space-y-1 max-h-[80px] overflow-y-auto">
                        {dayEvents.map((event, eventIndex) => (
                          <Link
                            href={event.type === "trip" ? `/trips/${event.tripId}` : "#"}
                            key={eventIndex}
                          >
                            <div
                              className={`text-xs p-1 rounded truncate ${
                                event.type === "trip"
                                  ? "bg-primary/10 text-primary"
                                  : "bg-secondary/10 text-secondary"
                              }`}
                            >
                              {event.title}
                            </div>
                          </Link>
                        ))}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Upcoming Events</h2>
            <div className="space-y-2">
              {filteredEvents
                .filter((event) => event.endDate >= new Date())
                .sort((a, b) => a.startDate.getTime() - b.startDate.getTime())
                .slice(0, 5)
                .map((event, index) => (
                  <Link href={event.type === "trip" ? `/trips/${event.tripId}` : "#"} key={index}>
                    <Card className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4 flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`h-10 w-10 rounded-full flex items-center justify-center ${
                              event.type === "trip" ? "bg-primary/10" : "bg-secondary/10"
                            }`}
                          >
                            {event.type === "trip" ? (
                              <MapPin className="h-5 w-5 text-primary" />
                            ) : (
                              <CalendarIcon className="h-5 w-5 text-secondary" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium">{event.title}</h3>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <CalendarIcon className="h-3 w-3" />
                                {event.startDate.toLocaleDateString("en-US", {
                                  month: "short",
                                  day: "numeric",
                                })}
                                {event.startDate.toDateString() !== event.endDate.toDateString() &&
                                  ` - ${event.endDate.toLocaleDateString("en-US", { month: "short", day: "numeric" })}`}
                              </span>
                              <span className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                {event.squad}
                              </span>
                            </div>
                          </div>
                        </div>
                        <Badge variant={event.type === "trip" ? "default" : "secondary"}>
                          {event.type === "trip" ? "Trip" : "Event"}
                        </Badge>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

function CalendarSkeleton() {
  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />

      <div className="flex-1 flex">
        <AppSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6">
            <h1 className="text-3xl font-bold">Calendar</h1>
            <p className="text-muted-foreground">View and manage your upcoming trips and events</p>
          </div>

          <Skeleton className="h-[600px] w-full mb-6" />

          <h2 className="text-xl font-semibold mb-4">Upcoming Events</h2>
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-20 w-full" />
            ))}
          </div>
        </main>
      </div>
    </div>
  )
}
