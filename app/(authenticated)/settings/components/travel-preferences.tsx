"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { <PERSON>lider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"

import { Loader2, DollarSign } from "lucide-react"
import { useUserPreferences } from "@/lib/domains/user-preferences/user-preferences.hooks"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { MonthSelector } from "@/components/month-selector"
import { DestinationAutocomplete } from "@/app/(authenticated)/trips/create/components/destination-autocomplete"
import {
  ALLOWED_TRAVEL_TYPES,
  ALLOWED_AVAILABILITY_PREFERENCES,
  ALLOWED_TRAVEL_GROUP_PREFERENCES,
} from "@/lib/constants/travel-types"

export function TravelPreferences() {
  const { preferences, loading: preferencesLoading, updatePreferences } = useUserPreferences()
  const user = useUser()

  // Local state
  const [selectedTravelTypes, setSelectedTravelTypes] = useState<string[]>(
    preferences?.travelPreferences || []
  )
  const [budgetRange, setBudgetRange] = useState<[number, number]>(
    Array.isArray(preferences?.budgetRange)
      ? (preferences.budgetRange as [number, number])
      : [500, 2000]
  )
  const [minBudget, setMinBudget] = useState<number>(budgetRange[0])
  const [maxBudget, setMaxBudget] = useState<number>(budgetRange[1])
  const [selectedAvailability, setSelectedAvailability] = useState<string[]>(
    preferences?.availabilityPreferences || []
  )
  const [selectedTravelGroups, setSelectedTravelGroups] = useState<string[]>(
    preferences?.travelGroupPreferences || []
  )
  const [selectedTravelSeasons, setSelectedTravelSeasons] = useState<string[]>(
    preferences?.preferredTravelSeasons || []
  )
  const [location, setLocation] = useState(preferences?.location || "")
  const [locationPlaceId, setLocationPlaceId] = useState<string | undefined>(
    preferences?.locationPlaceId || undefined
  )
  const [saving, setSaving] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)

  // Update local state when preferences change
  useEffect(() => {
    if (!preferencesLoading && preferences) {
      setSelectedTravelTypes(preferences.travelPreferences || [])
      const range: [number, number] = Array.isArray(preferences.budgetRange)
        ? (preferences.budgetRange as [number, number])
        : [500, 2000]
      setBudgetRange(range)
      setMinBudget(range[0])
      setMaxBudget(range[1])
      setSelectedAvailability(preferences.availabilityPreferences || [])
      setSelectedTravelGroups(preferences.travelGroupPreferences || [])
      setSelectedTravelSeasons(preferences.preferredTravelSeasons || [])
      setLocation(preferences.location || "")
      setLocationPlaceId(preferences.locationPlaceId || undefined)
      setInitialLoading(false)
    }
  }, [preferences, preferencesLoading])

  // Update budget range when min/max values change
  useEffect(() => {
    // Ensure max is always >= min
    const newMax = Math.max(minBudget, maxBudget)
    setBudgetRange([minBudget, newMax])
    if (newMax !== maxBudget) {
      setMaxBudget(newMax)
    }
  }, [minBudget, maxBudget])

  // Handle slider value changes
  const handleSliderChange = (value: number[]) => {
    const [min, max] = value as [number, number]
    setMinBudget(min)
    setMaxBudget(max)
  }

  const toggleTravelType = (type: string) => {
    // Check if the type already exists (case-insensitive)
    const typeExists = selectedTravelTypes.some((t) => t.toLowerCase() === type.toLowerCase())

    if (typeExists) {
      // Remove the type (case-insensitive)
      setSelectedTravelTypes(
        selectedTravelTypes.filter((t) => t.toLowerCase() !== type.toLowerCase())
      )
    } else {
      // Add the type with the exact case provided
      setSelectedTravelTypes([...selectedTravelTypes, type])
    }
  }

  const toggleAvailability = (availability: string) => {
    if (selectedAvailability.includes(availability)) {
      setSelectedAvailability(selectedAvailability.filter((a) => a !== availability))
    } else {
      setSelectedAvailability([...selectedAvailability, availability])
    }
  }

  const toggleTravelGroup = (group: string) => {
    if (selectedTravelGroups.includes(group)) {
      setSelectedTravelGroups(selectedTravelGroups.filter((g) => g !== group))
    } else {
      setSelectedTravelGroups([...selectedTravelGroups, group])
    }
  }

  const savePreferences = async () => {
    setSaving(true)
    try {
      await updatePreferences({
        travelPreferences: selectedTravelTypes,
        budgetRange: budgetRange,
        availabilityPreferences: selectedAvailability,
        travelGroupPreferences: selectedTravelGroups,
        preferredTravelSeasons: selectedTravelSeasons,
        location: location || "",
        locationPlaceId: locationPlaceId || "",
      })
    } catch (error) {
      console.error("Error updating preferences:", error)
    } finally {
      setSaving(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Travel Preferences</CardTitle>
        <CardDescription>
          Update your travel preferences for better trip recommendations
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {initialLoading ? (
          <div className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-sm text-muted-foreground">Loading travel preferences...</p>
          </div>
        ) : !user ? (
          <div className="flex flex-col items-center justify-center py-8">
            <p className="text-sm text-muted-foreground mb-4">Unable to load travel preferences.</p>
            <p className="text-sm text-muted-foreground">
              Please sign in to view your travel preferences.
            </p>
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <Label>Preferred Travel Types</Label>
              <div className="flex flex-wrap gap-2">
                {ALLOWED_TRAVEL_TYPES.map((type) => (
                  <Badge
                    key={type}
                    variant={
                      selectedTravelTypes.some((t) => t.toLowerCase() === type.toLowerCase())
                        ? "default"
                        : "outline"
                    }
                    className="cursor-pointer hover:bg-primary/10"
                    onClick={() => toggleTravelType(type)}
                  >
                    {type}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Typical Budget Range (per person)</Label>
              <div className="pt-2 px-4">
                <div className="space-y-4">
                  <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                    <div className="flex-1 space-y-2">
                      <Label htmlFor="min-budget" className="text-xs">
                        Minimum
                      </Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="min-budget"
                          type="number"
                          min={100}
                          max={5000}
                          step={100}
                          value={minBudget}
                          onChange={(e) => {
                            const value = Math.max(
                              100,
                              Math.min(5000, parseInt(e.target.value) || 100)
                            )
                            setMinBudget(value)
                          }}
                          className="pl-9"
                        />
                      </div>
                    </div>
                    <div className="flex-1 space-y-2">
                      <Label htmlFor="max-budget" className="text-xs">
                        Maximum
                      </Label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="max-budget"
                          type="number"
                          min={minBudget}
                          max={5000}
                          step={100}
                          value={maxBudget}
                          onChange={(e) => {
                            const value = Math.max(
                              minBudget,
                              Math.min(5000, parseInt(e.target.value) || minBudget)
                            )
                            setMaxBudget(value)
                          }}
                          className="pl-9"
                        />
                      </div>
                    </div>
                  </div>
                  <Slider
                    value={budgetRange}
                    min={100}
                    max={5000}
                    step={100}
                    onValueChange={handleSliderChange}
                  />
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>$100</span>
                    <span>$5000</span>
                  </div>
                  <div className="flex justify-center text-sm font-medium">
                    <span>
                      Selected: ${minBudget}
                      {minBudget !== maxBudget ? ` - $${maxBudget}` : ""}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Location (Optional)</Label>
              <DestinationAutocomplete
                value={location}
                onChange={(value, placeId) => {
                  setLocation(value)
                  setLocationPlaceId(placeId)
                }}
                placeholder="Where are you based?"
                required={false}
                label=""
              />
              <p className="text-xs text-muted-foreground">
                Help us provide better travel suggestions based on your location
              </p>
            </div>

            <div className="space-y-2">
              <Label>Preferred Travel Seasons</Label>
              <MonthSelector
                selectedMonths={selectedTravelSeasons}
                onChange={setSelectedTravelSeasons}
                onConfirm={() => {
                  // Optional: Add any additional logic when user confirms selection
                }}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Typical Availability</Label>
                <div className="flex flex-wrap gap-2">
                  {ALLOWED_AVAILABILITY_PREFERENCES.map((avail) => (
                    <Badge
                      key={avail}
                      variant={selectedAvailability.includes(avail) ? "default" : "outline"}
                      className="cursor-pointer hover:bg-primary/10"
                      onClick={() => toggleAvailability(avail)}
                    >
                      {avail === "Weekends" && "Weekends"}
                      {avail === "Week-long" && "Week-long trips"}
                      {avail === "2+ weeks" && "Extended trips (2+ weeks)"}
                      {avail === "Flexible" && "Flexible"}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="space-y-2">
                <Label>Typical Group Type</Label>
                <div className="flex flex-wrap gap-2">
                  {ALLOWED_TRAVEL_GROUP_PREFERENCES.map((group) => (
                    <Badge
                      key={group}
                      variant={selectedTravelGroups.includes(group) ? "default" : "outline"}
                      className="cursor-pointer hover:bg-primary/10"
                      onClick={() => toggleTravelGroup(group)}
                    >
                      {group === "Solo" && "Solo"}
                      {group === "Couples" && "Couple"}
                      {group === "Friends" && "Friends"}
                      {group === "Family w/ Kids" && "Family with Kids"}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={savePreferences} disabled={saving || initialLoading || !user}>
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Preferences"
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
