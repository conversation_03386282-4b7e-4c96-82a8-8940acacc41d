"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { toast } from "@/components/ui/use-toast"
import { BillingSettings as OriginalBillingSettings } from "./billing-settings"

export function BillingSettingsWrapper() {
  const searchParams = useSearchParams()
  const [toastShown, setToastShown] = useState(false)

  // Check for successful checkout session
  useEffect(() => {
    const sessionId = searchParams.get("session_id")
    const canceled = searchParams.get("canceled")

    // Only show toast if we haven't shown it yet for this session
    if (!toastShown) {
      if (sessionId) {
        toast({
          title: "Subscription successful!",
          description: "Your subscription has been activated.",
        })
        setToastShown(true)
      } else if (canceled) {
        toast({
          title: "Subscription canceled",
          description: "Your subscription process was canceled.",
          variant: "destructive",
        })
        setToastShown(true)
      }
    }
  }, [searchParams, toastShown])

  return <OriginalBillingSettings />
}
