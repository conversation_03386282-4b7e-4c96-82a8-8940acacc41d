"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, MapPin, Users } from "lucide-react"

export default function AdminDashboardPage() {
  const [stats, setStats] = useState({
    users: 0,
    trips: 0,
    squads: 0,
    suggestions: 0,
  })
  const [recentActivity, setRecentActivity] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        // In a real app, you would fetch actual data from Firebase
        // For now, we'll use mock data

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1000))

        setStats({
          users: 124,
          trips: 37,
          squads: 18,
          suggestions: 52,
        })

        setRecentActivity([
          {
            id: 1,
            type: "user_joined",
            user: {
              name: "<PERSON>",
              email: "<EMAIL>",
              avatar: "/placeholder.svg?height=40&width=40",
            },
            timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          },
          {
            id: 2,
            type: "trip_created",
            user: {
              name: "Michael Smith",
              email: "<EMAIL>",
              avatar: "/placeholder.svg?height=40&width=40",
            },
            tripName: "Vegas Weekend",
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
          },
          {
            id: 3,
            type: "squad_created",
            user: {
              name: "David Wilson",
              email: "<EMAIL>",
              avatar: "/placeholder.svg?height=40&width=40",
            },
            squadName: "College Buddies",
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
          },
          {
            id: 4,
            type: "suggestion_added",
            user: {
              name: "Admin",
              email: "<EMAIL>",
              avatar: "/placeholder.svg?height=40&width=40",
            },
            suggestionName: "Ski Trip to Aspen",
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8).toISOString(), // 8 hours ago
          },
        ])

        setLoading(false)
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return new Intl.RelativeTimeFormat("en", { numeric: "auto" }).format(
      Math.round((date.getTime() - Date.now()) / (1000 * 60 * 60)),
      "hour"
    )
  }

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Admin Dashboard</h1>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="flex flex-row items-center justify-between p-6">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Users</p>
              <p className="text-3xl font-bold">{stats.users}</p>
            </div>
            <div className="rounded-full bg-primary/10 p-3">
              <Users className="h-6 w-6 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex flex-row items-center justify-between p-6">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Active Trips</p>
              <p className="text-3xl font-bold">{stats.trips}</p>
            </div>
            <div className="rounded-full bg-primary/10 p-3">
              <MapPin className="h-6 w-6 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex flex-row items-center justify-between p-6">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Squads</p>
              <p className="text-3xl font-bold">{stats.squads}</p>
            </div>
            <div className="rounded-full bg-primary/10 p-3">
              <Users className="h-6 w-6 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex flex-row items-center justify-between p-6">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Suggestions</p>
              <p className="text-3xl font-bold">{stats.suggestions}</p>
            </div>
            <div className="rounded-full bg-primary/10 p-3">
              <BarChart className="h-6 w-6 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Activity Feed */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest actions across the platform</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-4">
                <Avatar>
                  <AvatarImage src={activity.user.avatar} alt={activity.user.name} />
                  <AvatarFallback>{activity.user.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <p className="font-medium">{activity.user.name}</p>
                    <Badge variant="outline" className="text-xs">
                      {activity.type === "user_joined" && "New User"}
                      {activity.type === "trip_created" && "New Trip"}
                      {activity.type === "squad_created" && "New Squad"}
                      {activity.type === "suggestion_added" && "New Suggestion"}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {activity.type === "user_joined" && "Joined the platform"}
                    {activity.type === "trip_created" && `Created trip "${activity.tripName}"`}
                    {activity.type === "squad_created" && `Created squad "${activity.squadName}"`}
                    {activity.type === "suggestion_added" &&
                      `Added suggestion "${activity.suggestionName}"`}
                  </p>
                  <p className="text-xs text-muted-foreground">{formatTime(activity.timestamp)}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
