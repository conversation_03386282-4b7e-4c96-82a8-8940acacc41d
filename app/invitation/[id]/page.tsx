"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Check, X, Users, LogIn, AlertTriangle } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { useAuthStatus } from "@/lib/domains/auth/auth.hooks"
import {
  updateInvitation,
  addMemberToSquad,
  type Invitation,
  getSquad,
} from "@/lib/firebase-service"
import { PageLoading } from "@/components/page-loading"

export default function InvitationPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user, loading: authLoading } = useAuthStatus()
  const [loading, setLoading] = useState(true)
  const [invitation, setInvitation] = useState<Invitation | null>(null)
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isExpired, setIsExpired] = useState(false)

  // First useEffect - fetch invitation data
  useEffect(() => {
    const fetchInvitation = async () => {
      if (!params.id) {
        setError("Invalid invitation link.")
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        const invitationId = params.id as string

        // Use the server API route to fetch invitation details
        // This avoids Firestore permission issues
        const response = await fetch(`/api/invitation/${invitationId}`)

        if (!response.ok) {
          const errorData = await response.json()
          console.error("Error response from invitation API:", errorData)

          if (response.status === 404) {
            setError("Invitation not found or has been cancelled.")
          } else {
            setError("There was a problem with this invitation. Please try again later.")
          }
          return
        }

        const invitationData = await response.json()

        // Check if the invitation is expired (based on creation date)
        if (invitationData.createdAt) {
          const createdDate = new Date(invitationData.createdAt._seconds * 1000)
          const oneWeekAgo = new Date()
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)

          if (createdDate < oneWeekAgo && invitationData.status === "pending") {
            setIsExpired(true)
            setError("This invitation has expired.")
            return
          }
        }

        setInvitation(invitationData)
      } catch (error) {
        console.error("Error fetching invitation:", error)
        // Generic error message for users
        setError("There was a problem with this invitation. Please try again later.")
      } finally {
        setLoading(false)
      }
    }

    fetchInvitation()
  }, [params.id])

  // Second useEffect - handle redirection for non-logged in users or email mismatch
  useEffect(() => {
    if (!authLoading && !loading && invitation) {
      // If user is not logged in, check if the invited user exists in our database
      if (!user) {
        // Check if inviteeId exists in the invitation
        // If inviteeId exists and is not empty, it means the user exists in our database
        if (invitation.inviteeId && invitation.inviteeId.trim() !== "") {
          console.log("User exists in database, redirecting to login")
          router.push(
            `/login?callback=${encodeURIComponent(`/invitation/${params.id}`)}&message=invitation_access`
          )
        } else {
          // If inviteeId doesn't exist or is empty, redirect to signup
          console.log("User doesn't exist in database, redirecting to signup")
          router.push(
            `/signup?callback=${encodeURIComponent(`/invitation/${params.id}`)}&invited_email=${encodeURIComponent(invitation.inviteeEmail)}`
          )
        }
      } else {
        // If user is logged in but with a different email than the invitation
        if (user.email?.toLowerCase() !== invitation.inviteeEmail.toLowerCase()) {
          console.log("Email mismatch:", {
            invitationEmail: invitation.inviteeEmail,
            userEmail: user.email,
          })
          // Show error toast
          toast({
            title: "Email mismatch",
            description: `This invitation was sent to ${invitation.inviteeEmail}, but you're logged in with ${user.email}. Please log in with the correct account.`,
            variant: "destructive",
          })

          // Set error state
          setError(
            `This invitation was sent to ${invitation.inviteeEmail}, but you're logged in with ${user.email}. Please log in with the correct account.`
          )
        } else {
          console.log("User logged in with correct email:", user.email)
        }
      }
    }
  }, [authLoading, loading, user, invitation, params.id, router, toast])

  const handleInvitation = async (status: "accepted" | "rejected") => {
    if (!user || !invitation) {
      toast({
        title: "Error",
        description: "Unable to process invitation. Please try again later.",
        variant: "destructive",
      })
      return
    }

    // Check if the invitation is for this user (by ID or email)
    if (
      invitation.inviteeId &&
      invitation.inviteeId !== user.uid &&
      invitation.inviteeEmail.toLowerCase() !== user.email?.toLowerCase()
    ) {
      toast({
        title: "Invalid invitation",
        description: "This invitation is not for your account.",
        variant: "destructive",
      })
      return
    }

    // Check if the invitation is still pending
    if (invitation.status !== "pending") {
      toast({
        title: "Invitation already processed",
        description: `This invitation has already been ${invitation.status}.`,
        variant: "destructive",
      })
      return
    }

    try {
      setProcessing(true)

      // If accepting, check if the user is already a member of the squad
      if (status === "accepted") {
        const squad = await getSquad(invitation.squadId)

        if (!squad) {
          toast({
            title: "Squad not found",
            description: "The squad no longer exists.",
            variant: "destructive",
          })
          setProcessing(false)
          return
        }

        // Check if user is already a member of the squad
        if (squad.members.includes(user.uid)) {
          // Update invitation status anyway
          await updateInvitation(invitation.id, status)

          toast({
            title: "Already a member",
            description: `You are already a member of ${invitation.squadName}`,
          })

          // Redirect to the squad page
          router.push(`/squads/${invitation.squadId}`)
          return
        }
      }

      // Get the auth token for API calls
      const token = localStorage.getItem("authToken")
      if (!token) {
        toast({
          title: "Authentication error",
          description: "Please log in again to process this invitation.",
          variant: "destructive",
        })
        return
      }

      // Update invitation status
      await updateInvitation(invitation.id, status)

      // If accepted, add user to squad
      if (status === "accepted") {
        await addMemberToSquad(invitation.squadId, user.uid)

        toast({
          title: "Invitation accepted",
          description: `You have joined ${invitation.squadName}`,
        })

        // Redirect to the squad page
        router.push(`/squads/${invitation.squadId}`)
      } else {
        toast({
          title: "Invitation rejected",
          description: "The invitation has been declined",
        })

        // Update the local invitation state
        setInvitation((prev) => (prev ? { ...prev, status } : null))
      }
    } catch (error) {
      console.error(`Error ${status === "accepted" ? "accepting" : "rejecting"} invitation:`, error)

      // Generic error message for users
      toast({
        title: "Error processing invitation",
        description: "We couldn't process your request at this time. Please try again later.",
        variant: "destructive",
      })

      // Set a generic error state
      setError("There was a problem processing this invitation. Please try again later.")
    } finally {
      setProcessing(false)
    }
  }

  if (authLoading || loading) {
    return <PageLoading message="Loading invitation..." />
  }

  if (error) {
    const isExpiredError = isExpired || (error && error.includes("expired"))
    const isEmailMismatch = error && error.includes("This invitation was sent to")

    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>
              {isExpiredError
                ? "Invitation Expired"
                : isEmailMismatch
                  ? "Email Mismatch"
                  : "Invitation Error"}
            </CardTitle>
            <CardDescription>
              {isExpiredError
                ? "This invitation link is no longer valid"
                : isEmailMismatch
                  ? "You're logged in with a different email"
                  : "There was a problem with this invitation"}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-destructive/10 p-2 rounded-full">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <p className="text-destructive">
                {isExpiredError
                  ? "This invitation has expired or is no longer valid."
                  : isEmailMismatch
                    ? error
                    : "We couldn't process this invitation at this time."}
              </p>
            </div>
            <p className="text-muted-foreground mt-2">
              {isExpiredError
                ? "Please contact the squad leader to request a new invitation."
                : isEmailMismatch
                  ? "Please log out and sign in with the correct account, or contact the squad leader to send a new invitation to your current email."
                  : "Please try again later or contact the squad leader for assistance."}
            </p>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            {user ? (
              <>
                <Link href="/dashboard" className="w-full">
                  <Button className="w-full">Go to Dashboard</Button>
                </Link>
                {isEmailMismatch && (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={async () => {
                      // Sign out and redirect to login
                      const { auth } = await import("@/lib/firebase")
                      await auth.signOut()
                      router.push(
                        `/login?callback=${encodeURIComponent(`/invitation/${params.id}`)}&message=invitation_access`
                      )
                    }}
                  >
                    <LogIn className="mr-2 h-4 w-4" /> Sign Out and Log In with Different Account
                  </Button>
                )}
              </>
            ) : (
              <Link
                href={`/login?callback=${encodeURIComponent(`/invitation/${params.id}`)}&message=invitation_access`}
                className="w-full"
              >
                <Button variant="outline" className="w-full">
                  <LogIn className="mr-2 h-4 w-4" /> Log In to View Invitation
                </Button>
              </Link>
            )}
          </CardFooter>
        </Card>
      </div>
    )
  }

  if (!invitation) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invitation Not Found</CardTitle>
            <CardDescription>
              This invitation may have been cancelled or doesn't exist
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-3 mb-4">
              <div className="bg-destructive/10 p-2 rounded-full">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <p className="text-destructive">
                The invitation you're looking for could not be found.
              </p>
            </div>
            <p className="text-muted-foreground mt-2">
              This may be because the invitation was cancelled, already accepted, or the link is
              incorrect.
            </p>
          </CardContent>
          <CardFooter>
            {user ? (
              <Link href="/dashboard" className="w-full">
                <Button className="w-full">Go to Dashboard</Button>
              </Link>
            ) : (
              <Link href="/login" className="w-full">
                <Button variant="outline" className="w-full">
                  <LogIn className="mr-2 h-4 w-4" /> Log In
                </Button>
              </Link>
            )}
          </CardFooter>
        </Card>
      </div>
    )
  }

  // User authentication is now handled by middleware

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Squad Invitation</CardTitle>
          <CardDescription>
            You've been invited to join {invitation.squadName}
            {invitation.status !== "pending" && (
              <span className="ml-2 capitalize">• {invitation.status}</span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-primary/10 p-2 rounded-full">
              <Users className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p>
                <span className="font-medium">{invitation.inviterName}</span> has invited you to
                join their squad.
              </p>
            </div>
          </div>

          {invitation.status === "pending" ? (
            <p className="text-muted-foreground mb-4">
              Join this squad to plan trips and adventures together.
            </p>
          ) : invitation.status === "accepted" ? (
            <p className="text-green-600 mb-4">
              You have accepted this invitation and joined the squad.
            </p>
          ) : (
            <p className="text-red-600 mb-4">You have declined this invitation.</p>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          {invitation.status === "pending" ? (
            <>
              <Button
                variant="outline"
                onClick={() => handleInvitation("rejected")}
                disabled={processing}
              >
                <X className="mr-2 h-4 w-4" /> Decline
              </Button>
              <Button onClick={() => handleInvitation("accepted")} disabled={processing}>
                <Check className="mr-2 h-4 w-4" /> Accept
              </Button>
            </>
          ) : invitation.status === "accepted" ? (
            <Link href={`/squads/${invitation.squadId}`} className="w-full">
              <Button className="w-full">Go to Squad</Button>
            </Link>
          ) : (
            <Link href="/dashboard" className="w-full">
              <Button variant="outline" className="w-full">
                Return to Dashboard
              </Button>
            </Link>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}
