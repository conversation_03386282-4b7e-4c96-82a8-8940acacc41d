import { NextRequest, NextResponse } from "next/server"
import { createCheckoutSession } from "@/lib/stripe"
import { getAdminInstance } from "@/lib/firebase-admin"

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json()
    const { priceId, mode = "subscription" } = body

    // Verify the user is authenticated
    const authHeader = request.headers.get("Authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const token = authHeader.split("Bearer ")[1]

    // Initialize Firebase Admin
    const { adminAuth } = await getAdminInstance()
    if (!adminAuth) {
      return NextResponse.json({ error: "Server configuration error" }, { status: 500 })
    }

    let decodedToken
    try {
      decodedToken = await adminAuth.verifyIdToken(token)
    } catch (error) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 })
    }

    const userId = decodedToken.uid
    const userEmail = decodedToken.email

    if (!userId || !userEmail) {
      return NextResponse.json({ error: "User information missing" }, { status: 400 })
    }

    if (!priceId) {
      return NextResponse.json({ error: "Price ID is required" }, { status: 400 })
    }

    // Create a checkout session
    const checkoutSession = await createCheckoutSession({
      priceId,
      userId,
      customerEmail: userEmail,
      mode,
    })

    return NextResponse.json({ sessionId: checkoutSession.id, url: checkoutSession.url })
  } catch (error) {
    console.error("Error creating checkout session:", error)
    return NextResponse.json({ error: "Failed to create checkout session" }, { status: 500 })
  }
}
