import { NextRequest, NextResponse } from "next/server"
import { createCustomerPortalSession } from "@/lib/stripe"
import { getAdminInstance } from "@/lib/firebase-admin"

export async function POST(request: NextRequest) {
  try {
    // Verify the user is authenticated
    const authHeader = request.headers.get("Authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const token = authHeader.split("Bearer ")[1]

    // Initialize Firebase Admin
    const { adminAuth } = await getAdminInstance()
    if (!adminAuth) {
      return NextResponse.json({ error: "Server configuration error" }, { status: 500 })
    }

    let decodedToken
    try {
      decodedToken = await adminAuth.verifyIdToken(token)
    } catch (error) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 })
    }

    const userId = decodedToken.uid

    // Get the user's subscription from Firestore using Admin SDK to get their Stripe customer ID
    const { adminDb } = await getAdminInstance()
    if (!adminDb) {
      return NextResponse.json({ error: "Server configuration error" }, { status: 500 })
    }

    // First check the userSubscriptions collection
    console.log(`Getting subscription document for userId: ${userId}`)
    const subscriptionDoc = await adminDb.collection("userSubscriptions").doc(userId).get()

    let stripeCustomerId = null

    if (subscriptionDoc.exists) {
      const subscriptionData = subscriptionDoc.data()
      console.log(`Subscription document found for userId: ${userId}`)
      console.log(`Subscription data:`, JSON.stringify(subscriptionData, null, 2))

      stripeCustomerId = subscriptionData?.stripeCustomerId
    }

    // If no subscription document or no customer ID, fall back to user document for backward compatibility
    if (!stripeCustomerId) {
      console.log(`No Stripe customer ID found in subscription, checking user document`)
      const userDoc = await adminDb.collection("users").doc(userId).get()

      if (userDoc.exists) {
        const userData = userDoc.data()
        console.log(`User document found for userId: ${userId}`)
        console.log(`User data:`, JSON.stringify(userData, null, 2))

        stripeCustomerId = userData?.stripeCustomerId
      }
    }

    // Check if we found a Stripe customer ID
    if (!stripeCustomerId) {
      console.error(`User does not have a Stripe customer ID: ${userId}`)
      return NextResponse.json(
        {
          error: "User does not have a Stripe customer ID",
          message: "You need to subscribe first before you can manage your subscription.",
        },
        { status: 400 }
      )
    }
    console.log(`User has Stripe customer ID: ${stripeCustomerId}`)

    // Create a customer portal session
    const portalSession = await createCustomerPortalSession(stripeCustomerId)

    return NextResponse.json({ url: portalSession.url })
  } catch (error) {
    console.error("Error creating customer portal session:", error)
    return NextResponse.json({ error: "Failed to create customer portal session" }, { status: 500 })
  }
}
