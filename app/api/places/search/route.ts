import { NextRequest, NextResponse } from "next/server"
import { verifyAuth } from "@/lib/api-auth"

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using the new auth system
    const authResult = await verifyAuth(request)

    if (!authResult.isAuthenticated) {
      // Return the error response if authentication failed
      return authResult.response
    }

    const userId = authResult.userId

    // Log the places API request (optional)
    console.log(`Places Search API request from user ${userId || "unknown"}`)
    const searchParams = request.nextUrl.searchParams
    const query = searchParams.get("query")

    if (!query) {
      return NextResponse.json({ error: "Query parameter is required" }, { status: 400 })
    }

    const apiKey = process.env.GOOGLE_PLACES_API_KEY

    if (!apiKey) {
      console.error("Google Places API key is not configured")
      return NextResponse.json({ error: "Places service is not configured" }, { status: 500 })
    }

    // Search for places using the text search API
    const searchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&type=tourist_attraction&key=${apiKey}`

    const response = await fetch(searchUrl)

    if (!response.ok) {
      const errorData = await response.json()
      console.error("Google Places API error:", errorData)
      return NextResponse.json({ error: "Failed to fetch place data" }, { status: response.status })
    }

    const data = await response.json()

    // Check if we found any places
    if (data.status !== "OK" || !data.results || data.results.length === 0) {
      console.error("No places found for query:", query)
      return NextResponse.json({
        places: [],
      })
    }

    // Format the results
    const places = data.results.map((place) => ({
      placeId: place.place_id,
      name: place.name,
      formattedAddress: place.formatted_address,
      location: place.geometry?.location,
      types: place.types,
      hasPhoto: !!place.photos,
    }))

    return NextResponse.json({ places })
  } catch (error) {
    console.error("Error in Google Places Search API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
