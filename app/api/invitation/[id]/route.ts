import { NextRequest, NextResponse } from "next/server"
import { getInvitationById } from "@/lib/server/invitation-service"

/**
 * API route to fetch invitation details using Firebase Admin SDK
 * This route does not require authentication
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: invitationId } = await params

    if (!invitationId) {
      return NextResponse.json({ error: "Invitation ID is required" }, { status: 400 })
    }

    // Get the invitation details using Firebase Admin SDK
    const invitation = await getInvitationById(invitationId)

    if (!invitation) {
      return NextResponse.json({ error: "Invitation not found" }, { status: 404 })
    }

    // Return a safe version of the invitation (omit any sensitive information if needed)
    return NextResponse.json({
      id: invitation.id,
      squadId: invitation.squadId,
      squadName: invitation.squadName,
      inviteeId: invitation.inviteeId,
      inviterName: invitation.inviterName,
      inviteeEmail: invitation.inviteeEmail,
      status: invitation.status,
      createdAt: invitation.createdAt,
      lastUpdated: invitation.lastUpdated,
    })
  } catch (error) {
    console.error("Error fetching invitation:", error)
    return NextResponse.json(
      {
        error: "Failed to fetch invitation details",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    )
  }
}
