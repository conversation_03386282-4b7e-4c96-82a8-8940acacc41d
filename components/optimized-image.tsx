"use client"

import { useState } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  aspectRatio?: "auto" | "square" | "video" | "portrait" | "wide"
  fill?: boolean
  priority?: boolean
  quality?: number
  sizes?: string
  fallbackSrc?: string
  onLoad?: () => void
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  aspectRatio = "auto",
  fill = false,
  priority = false,
  quality = 80,
  sizes,
  fallbackSrc = "/placeholder.svg",
  onLoad,
}: OptimizedImageProps) {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)

  // Define aspect ratio classes
  const aspectRatioClasses = {
    auto: "",
    square: "aspect-square",
    video: "aspect-video",
    portrait: "aspect-[3/4]",
    wide: "aspect-[2/1]",
  }

  // Handle image load complete
  const handleLoadComplete = () => {
    setLoading(false)
    if (onLoad) onLoad()
  }

  // Handle image load error
  const handleError = () => {
    setError(true)
    setLoading(false)
  }

  // Determine if we should use Next.js Image optimization
  const useNextImage = src && !src.startsWith("data:") && !error

  // For external URLs that don't work with Next.js Image, we need to add them to next.config.js domains
  const isExternalUrl = src && (src.startsWith("http://") || src.startsWith("https://"))

  return (
    <div className={cn("relative overflow-hidden", aspectRatioClasses[aspectRatio], className)}>
      {loading && <Skeleton className="absolute inset-0 z-10" />}

      {useNextImage ? (
        <Image
          src={src}
          alt={alt}
          width={fill ? undefined : width || 1200}
          height={fill ? undefined : height || 800}
          className={cn(
            "object-cover transition-opacity duration-300",
            loading ? "opacity-0" : "opacity-100",
            fill ? "absolute inset-0" : "w-full h-full"
          )}
          fill={fill}
          priority={priority}
          quality={quality}
          sizes={sizes || "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"}
          onLoad={handleLoadComplete}
          onError={handleError}
        />
      ) : (
        <img
          src={error ? fallbackSrc : src}
          alt={alt}
          className={cn(
            "object-cover w-full h-full transition-opacity duration-300",
            loading ? "opacity-0" : "opacity-100"
          )}
          onLoad={handleLoadComplete}
          onError={handleError}
        />
      )}
    </div>
  )
}
