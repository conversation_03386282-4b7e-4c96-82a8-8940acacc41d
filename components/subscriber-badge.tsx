"use client"

import { <PERSON> } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface SubscriberBadgeProps {
  className?: string
}

export function SubscriberBadge({ className = "" }: SubscriberBadgeProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={`inline-flex ${className}`}>
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
          </span>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">Pro Subscriber</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
