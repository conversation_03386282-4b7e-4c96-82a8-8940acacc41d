"use client"

// import { AppHeader } from "@/components/app-header"
// import { AppSidebar } from "@/components/app-sidebar"

interface PageLoadingProps {
  message?: string
}

export function PageLoading({ message = "Loading..." }: PageLoadingProps) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* <AppHeader /> */}
      <div className="flex-1 flex">
        {/* <AppSidebar /> */}
        <main className="flex-1 p-6 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-muted-foreground">{message}</p>
          </div>
        </main>
      </div>
    </div>
  )
}
