"use client"

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { SubscriberBadge } from "@/components/subscriber-badge"

interface UserDisplayProps {
  displayName: string | null
  photoURL?: string | null
  isSubscriber?: boolean
  size?: "sm" | "md" | "lg"
  showBadge?: boolean
}

export function UserDisplay({
  displayName,
  photoURL,
  isSubscriber = false,
  size = "md",
  showBadge = true,
}: UserDisplayProps) {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-10 w-10",
  }

  return (
    <div className="flex items-center gap-2">
      <Avatar className={sizeClasses[size]}>
        <AvatarImage src={photoURL || undefined} alt={displayName || "User"} />
        <AvatarFallback>{displayName?.charAt(0) || "U"}</AvatarFallback>
      </Avatar>
      <div className="flex items-center gap-1">
        <span className="font-medium">{displayName}</span>
        {isSubscriber && showBadge && <SubscriberBadge className="ml-1" />}
      </div>
    </div>
  )
}
