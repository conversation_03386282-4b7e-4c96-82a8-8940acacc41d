"use client"

import React from "react"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { StoreInitializer } from "@/components/store-initializer"

/**
 * Providers component
 *
 * This component initializes essential global providers:
 * - StoreInitializer: Initializes essential global stores (theme, sidebar, auth token)
 * - ThemeProvider: Provides theme context from next-themes
 * - Toaster: Provides toast notifications
 *
 * Domain-specific stores are initialized in their respective components or layouts.
 */
export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <>
      {/* Initialize only essential global stores */}
      <StoreInitializer />

      {/* ThemeProvider for next-themes integration */}
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange={false}
      >
        {children}
        <Toaster />
      </ThemeProvider>
    </>
  )
}
