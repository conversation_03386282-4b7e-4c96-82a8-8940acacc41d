"use client"

import { useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { auth } from "@/lib/firebase"
import { useAuthState } from "react-firebase-hooks/auth"
import { useAuthStore as useZustandAuthStore } from "@/lib/stores/auth-store"

// Export the Zustand store for direct access
export { useAuthStore as useZustandAuthStore } from "@/lib/stores/auth-store"

// No need to import getUser as it's now handled in the auth store

// Create selector hooks for specific parts of the auth state
export const useUser = () => useZustandAuthStore((state) => state.user)
export const useUserData = () => useZustandAuthStore((state) => state.userData)
export const useUserDataLoading = () => useZustandAuthStore((state) => state.userDataLoading)
export const useAuthLoading = () => useZustandAuthStore((state) => state.loading)
export const useAuthError = () => useZustandAuthStore((state) => state.error)
export const useIsAdmin = () => useZustandAuthStore((state) => state.isAdmin)

// Enhanced hook that combines the auth store with Firebase auth state
export function useAuthStore() {
  // Get auth store state and actions
  const authStore = useZustandAuthStore()
  return authStore
}

// Hook to sync Firebase auth state with Zustand store
export function useAuthSync() {
  const [user, loading, error] = useAuthState(auth)
  const router = useRouter()
  const pathname = usePathname()

  // Get auth store actions
  const { setUser, setLoading, setError, isProtectedRoute, refreshAdminStatus } =
    useZustandAuthStore()

  // Refresh admin status when user changes
  useEffect(() => {
    if (user) {
      refreshAdminStatus()
    }
  }, [user, refreshAdminStatus])

  // Update auth store with Firebase auth state
  useEffect(() => {
    setUser(user || null)
    setLoading(loading)
    setError(error || null)
  }, [user, loading, error, setUser, setLoading, setError])

  // Redirect unauthenticated users away from protected routes
  useEffect(() => {
    if (!loading && !user && isProtectedRoute(pathname || "")) {
      // Determine appropriate message code based on the path
      let messageCode = "unauthorized"

      // For invitation links, we'll let the invitation page handle the redirection
      if (pathname?.startsWith("/invitation/")) {
        // Don't redirect here - let the invitation page handle it
        console.log(`Skipping auth redirect for invitation page: ${pathname}`)
      } else {
        // For other protected routes, just use the general unauthorized message
        router.push(`/login?message=${messageCode}`)
      }
    }
  }, [user, loading, pathname, router, isProtectedRoute])

  return { user, loading, error }
}

// Convenience hook for getting user data from the auth store
export function useUserWithData() {
  const user = useUser()
  const userData = useUserData()
  const loading = useUserDataLoading()
  const fetchUserData = useZustandAuthStore((state) => state.fetchUserData)

  // Fetch user data when user changes or when the component mounts
  useEffect(() => {
    if (user) {
      fetchUserData()
    }
  }, [user, fetchUserData])

  return { user, userData, loading }
}

// Convenience hook for auth status (user and loading)
export function useAuthStatus() {
  const user = useUser()
  const loading = useAuthLoading()
  return { user, loading }
}
