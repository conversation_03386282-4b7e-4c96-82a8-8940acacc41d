"use client"

import { useEffect } from "react"
import { useSubscriptionStore } from "@/lib/stores/subscription-store"
import { useAuthLoading } from "./use-auth-store"
import { useRouter } from "next/navigation"
import { SubscriptionErrorType } from "@/lib/subscription-errors"

/**
 * Selectors for subscription store
 */

// Basic state selectors
export const useSubscription = () => useSubscriptionStore()
export const useSubscriptionLoading = () => useSubscriptionStore((state) => state.loading)
export const useIsSubscribed = () => useSubscriptionStore((state) => state.isSubscribed)
export const useSubscriptionPlan = () => useSubscriptionStore((state) => state.subscriptionPlan)
export const useSubscriptionStatus = () => useSubscriptionStore((state) => state.subscriptionStatus)
export const useSubscriptionDetails = () => useSubscriptionStore((state) => state.subscription)

// Limit selectors
export const useMaxSquads = () => useSubscriptionStore((state) => state.maxSquads)
export const useMaxTripsPerSquad = () => useSubscriptionStore((state) => state.maxTripsPerSquad)
export const useMaxDailyAIRequests = () => useSubscriptionStore((state) => state.maxDailyAIRequests)
export const useMaxWeeklyAIRequests = () =>
  useSubscriptionStore((state) => state.maxWeeklyAIRequests)

// Action selectors
export const useFetchSubscription = () => useSubscriptionStore((state) => state.fetchSubscription)
export const useCanCreateMoreSquads = () =>
  useSubscriptionStore((state) => state.canCreateMoreSquads)
export const useCanCreateMoreTripsInSquad = () =>
  useSubscriptionStore((state) => state.canCreateMoreTripsInSquad)
export const useCanMakeAIRequest = () => useSubscriptionStore((state) => state.canMakeAIRequest)
export const useIncrementAIUsage = () => useSubscriptionStore((state) => state.incrementAIUsage)
export const useGetAIUsage = () => useSubscriptionStore((state) => state.getAIUsage)

/**
 * Enhanced subscription hook with auto-initialization
 */
export const useSubscriptionWithInit = () => {
  const authLoading = useAuthLoading()
  const router = useRouter()
  const store = useSubscriptionStore()
  const { fetchSubscription, refreshSubscriptionIfNeeded } = store

  // Fetch subscription data when auth state changes
  useEffect(() => {
    if (!authLoading) {
      fetchSubscription()
    }
  }, [authLoading, fetchSubscription])

  // Add a periodic refresh for very long sessions
  useEffect(() => {
    // Check when component mounts and when window regains focus
    refreshSubscriptionIfNeeded()

    const handleFocus = () => {
      refreshSubscriptionIfNeeded()
    }

    window.addEventListener("focus", handleFocus)
    return () => window.removeEventListener("focus", handleFocus)
  }, [refreshSubscriptionIfNeeded])

  // Create a router-aware error handler
  const handleSubscriptionError = (errorType: SubscriptionErrorType) => {
    store.handleSubscriptionErrorWithRouter(errorType, router)
  }

  // Return the store with the router-aware error handler
  return {
    ...store,
    handleSubscriptionError,
  }
}
