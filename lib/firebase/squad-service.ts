import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
  arrayUnion,
  arrayRemove,
} from "firebase/firestore"
import { User, getUserByEmail } from "./user-service"

// Types
export interface Squad {
  id: string
  name: string
  description?: string
  leaderId: string
  members: string[] // Array of user IDs
  createdAt: Timestamp | null
}

// Squad operations
export const createSquad = async (squadData: Omit<Squad, "id" | "createdAt">) => {
  try {
    const squadRef = doc(collection(db, "squads"))
    const squadId = squadRef.id

    await setDoc(squadRef, {
      ...squadData,
      id: squadId,
      createdAt: serverTimestamp(),
    })

    return squadId
  } catch (error) {
    console.error("Error creating squad:", error)
    throw error
  }
}

export const getSquad = async (squadId: string) => {
  try {
    const squadDoc = await getDoc(doc(db, "squads", squadId))
    if (squadDoc.exists()) {
      const data = squadDoc.data()
      // Ensure members is always an array
      const members = Array.isArray(data.members) ? data.members : []
      return { ...data, id: squadId, members } as Squad
    }
    return null
  } catch (error) {
    console.error("Error getting squad:", error)
    throw error
  }
}

export const getUserSquads = async (userId: string) => {
  try {
    const q = query(collection(db, "squads"), where("members", "array-contains", userId))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map((doc) => {
      const data = doc.data()
      // Ensure members is always an array
      const members = Array.isArray(data.members) ? data.members : []
      return { ...data, id: doc.id, members } as Squad
    })
  } catch (error) {
    console.error("Error getting user squads:", error)
    throw error
  }
}

export const updateSquad = async (squadId: string, squadData: Partial<Squad>) => {
  try {
    await updateDoc(doc(db, "squads", squadId), squadData)
    return { success: true }
  } catch (error) {
    console.error("Error updating squad:", error)
    throw error
  }
}

export const addMemberToSquad = async (squadId: string, userId: string) => {
  try {
    await updateDoc(doc(db, "squads", squadId), {
      members: arrayUnion(userId),
    })
    return { success: true }
  } catch (error) {
    console.error("Error adding member to squad:", error)
    throw error
  }
}

export const removeMemberFromSquad = async (squadId: string, userId: string) => {
  try {
    await updateDoc(doc(db, "squads", squadId), {
      members: arrayRemove(userId),
    })
    return { success: true }
  } catch (error) {
    console.error("Error removing member from squad:", error)
    throw error
  }
}
