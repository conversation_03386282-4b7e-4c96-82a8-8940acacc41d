import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
} from "firebase/firestore"
import { getUserSquads } from "./squad-service"
import { getSquadTrips } from "./trip-service"
import { hasActiveSubscription as checkActiveSubscription } from "./subscription-service"

// Types
export interface User {
  uid: string
  email: string
  displayName: string
  photoURL?: string
  bio?: string
  createdAt: Timestamp | null
  travelPreferences?: string[]
  budgetRange?: string | [number, number]
  availabilityPreferences?: string[]
  preferredTravelSeasons?: string[]
  travelGroupPreferences?: string[]
  isAdmin?: boolean
  theme?: string
  // Subscription related fields
  stripeCustomerId?: string
  subscriptionId?: string
  subscriptionStatus?: "active" | "canceled" | "past_due" | "trialing" | "incomplete" | null
  subscriptionPlan?: "free" | "monthly" | "yearly"
  subscriptionCurrentPeriodEnd?: Timestamp | null
  // AI usage tracking fields
  aiUsageToday?: number
  aiUsageThisWeek?: number
  aiUsageLastReset?: Timestamp | null
  aiUsageWeekStart?: Timestamp | null
}

// User operations
export const getUser = async (userId: string) => {
  try {
    const userDoc = await getDoc(doc(db, "users", userId))
    if (userDoc.exists()) {
      return { ...userDoc.data(), uid: userId } as User
    }
    return null
  } catch (error) {
    console.error("Error getting user:", error)
    throw error
  }
}

export const getUserByEmail = async (email: string) => {
  try {
    const usersRef = collection(db, "users")
    const q = query(usersRef, where("email", "==", email))
    const querySnapshot = await getDocs(q)

    if (!querySnapshot.empty) {
      const userDoc = querySnapshot.docs[0]
      return { ...userDoc.data(), uid: userDoc.id } as User
    }

    return null
  } catch (error) {
    console.error("Error getting user by email:", error)
    throw error
  }
}

export const getUsersFromIds = async (userIds: string[]) => {
  try {
    // Ensure userIds is an array
    if (!Array.isArray(userIds)) {
      console.error("userIds is not an array:", userIds)
      return []
    }

    const users: User[] = []
    for (const userId of userIds) {
      const user = await getUser(userId)
      if (user) {
        users.push(user)
      }
    }
    return users
  } catch (error) {
    console.error("Error getting users from IDs:", error)
    throw error
  }
}

export const ensureUserDocument = async (user: any) => {
  if (!user) return null

  try {
    const userDoc = await getDoc(doc(db, "users", user.uid))
    if (!userDoc.exists()) {
      // Create user document if it doesn't exist
      await setDoc(doc(db, "users", user.uid), {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName || "",
        photoURL: user.photoURL || "",
        createdAt: serverTimestamp(),
        subscriptionPlan: "free",
        subscriptionStatus: null,
      })
    }
    return userDoc.exists() ? ({ ...userDoc.data(), uid: user.uid } as User) : null
  } catch (error) {
    console.error("Error ensuring user document:", error)
    return null
  }
}

// Add a function to update user preferences
export const updateUserPreferences = async (userId: string, preferences: Partial<User>) => {
  try {
    await updateDoc(doc(db, "users", userId), preferences)
    return { success: true }
  } catch (error) {
    console.error("Error updating user preferences:", error)
    throw error
  }
}

// Add a function to update user theme preference
export const updateUserTheme = async (userId: string, theme: string) => {
  try {
    await updateDoc(doc(db, "users", userId), { theme })
    return { success: true }
  } catch (error) {
    console.error("Error updating user theme:", error)
    throw error
  }
}

// Subscription related functions
export const updateUserSubscription = async (
  userId: string,
  subscriptionData: {
    stripeCustomerId?: string
    subscriptionId?: string
    subscriptionStatus?: "active" | "canceled" | "past_due" | "trialing" | "incomplete" | null
    subscriptionPlan?: "free" | "monthly" | "yearly"
    subscriptionCurrentPeriodEnd?: Timestamp | Date | null
  }
) => {
  try {
    await updateDoc(doc(db, "users", userId), subscriptionData)
    return { success: true }
  } catch (error) {
    console.error("Error updating user subscription:", error)
    throw error
  }
}

// Check if user has an active subscription
// DEPRECATED: Use hasActiveSubscription from subscription-service instead
export const hasActiveSubscription = async (user: User | null): Promise<boolean> => {
  if (!user) return false

  // Use the subscription service to check subscription status
  return await checkActiveSubscription(user.uid)
}

// Check if user can create more squads
// DEPRECATED: Use canCreateMoreSquads from subscription-provider instead
export const canCreateMoreSquads = async (userId: string): Promise<boolean> => {
  try {
    const user = await getUser(userId)
    if (!user) return false

    // If user has an active subscription, they can create unlimited squads
    if (await hasActiveSubscription(user)) return true

    // Otherwise, check if they've reached the free plan limit
    const userSquads = await getUserSquads(userId)
    return userSquads.length < 3 // Free plan limit is 3 squads
  } catch (error) {
    console.error("Error checking if user can create more squads:", error)
    return false
  }
}

// Check if user can create more trips in a squad
// DEPRECATED: Use canCreateMoreTripsInSquad from subscription-provider instead
export const canCreateMoreTripsInSquad = async (
  userId: string,
  squadId: string
): Promise<boolean> => {
  try {
    const user = await getUser(userId)
    if (!user) return false

    // If user has an active subscription, they can create unlimited trips
    if (await hasActiveSubscription(user)) return true

    // Otherwise, check if they've reached the free plan limit for this squad
    const squadTrips = await getSquadTrips(squadId)
    const activeTrips = squadTrips.filter(
      (trip) => trip.status === "planning" || trip.status === "upcoming" || trip.status === "active"
    )
    return activeTrips.length < 2 // Free plan limit is 2 active trips per squad
  } catch (error) {
    console.error("Error checking if user can create more trips in squad:", error)
    return false
  }
}
