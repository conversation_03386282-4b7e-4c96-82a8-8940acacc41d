import { db } from "../firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
  deleteDoc,
} from "firebase/firestore"
import { User, getUserByEmail } from "./user-service"
import { Squad, getSquad } from "./squad-service"

// Types
export interface Invitation {
  id: string
  squadId: string
  squadName: string
  inviterId: string
  inviterName: string
  inviteeId: string
  inviteeEmail: string
  status: "pending" | "accepted" | "rejected"
  createdAt: Timestamp | null
  lastUpdated?: Timestamp | null
  lastResent?: Timestamp | null
}

// Invitation operations
export const createInvitation = async (data: {
  squadId: string
  squadName: string
  inviterId: string
  inviterName: string
  inviteeId: string
  inviteeEmail: string
}) => {
  try {
    const invitationRef = doc(collection(db, "invitations"))
    const invitationId = invitationRef.id

    await setDoc(invitationRef, {
      ...data,
      id: invitationId,
      status: "pending",
      createdAt: serverTimestamp(),
    })

    return invitationId
  } catch (error) {
    console.error("Error creating invitation:", error)
    throw error
  }
}

export const getUserInvitations = async (
  userId: string,
  status?: "pending" | "accepted" | "rejected"
) => {
  try {
    let q
    if (status) {
      q = query(
        collection(db, "invitations"),
        where("inviteeId", "==", userId),
        where("status", "==", status)
      )
    } else {
      q = query(collection(db, "invitations"), where("inviteeId", "==", userId))
    }
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Invitation)
  } catch (error) {
    console.error("Error getting user invitations:", error)
    throw error
  }
}

export const getSquadInvitations = async (squadId: string) => {
  try {
    const q = query(collection(db, "invitations"), where("squadId", "==", squadId))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Invitation)
  } catch (error) {
    console.error("Error getting squad invitations:", error)
    throw error
  }
}

export const updateInvitation = async (invitationId: string, status: "accepted" | "rejected") => {
  try {
    await updateDoc(doc(db, "invitations", invitationId), {
      status,
      lastUpdated: serverTimestamp(),
    })
    return { success: true }
  } catch (error) {
    console.error("Error updating invitation:", error)
    throw error
  }
}

export const cancelInvitation = async (invitationId: string) => {
  try {
    await deleteDoc(doc(db, "invitations", invitationId))
    return { success: true }
  } catch (error) {
    console.error("Error canceling invitation:", error)
    throw error
  }
}

export const resendInvitation = async (invitationId: string) => {
  try {
    const invitationDoc = await getDoc(doc(db, "invitations", invitationId))
    if (!invitationDoc.exists()) {
      return { success: false, error: "Invitation not found" }
    }

    const invitation = invitationDoc.data() as Invitation

    // Check if the invitation can be resent (5 minutes cooldown)
    if (invitation.lastResent) {
      const lastResent = invitation.lastResent.toDate()
      const fiveMinutesAgo = new Date()
      fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5)

      if (lastResent > fiveMinutesAgo) {
        return {
          success: false,
          error: "This invitation can only be resent once every 5 minutes",
          nextResendDate: new Date(lastResent.getTime() + 5 * 60 * 1000),
        }
      }
    }

    // Update the invitation status back to pending and set lastResent
    await updateDoc(doc(db, "invitations", invitationId), {
      status: "pending",
      lastResent: serverTimestamp(),
      lastUpdated: serverTimestamp(),
    })

    return { success: true }
  } catch (error) {
    console.error("Error resending invitation:", error)
    throw error
  }
}

export const canResendInvitation = async (invitationId: string) => {
  try {
    const invitationDoc = await getDoc(doc(db, "invitations", invitationId))
    if (!invitationDoc.exists()) {
      return { canResend: false, error: "Invitation not found" }
    }

    const invitation = invitationDoc.data() as Invitation

    // Check if the invitation is rejected (only rejected invitations can be resent)
    if (invitation.status !== "rejected") {
      return { canResend: false, error: "Only rejected invitations can be resent" }
    }

    // Check if the invitation can be resent (5 minutes cooldown)
    if (invitation.lastResent) {
      const lastResent = invitation.lastResent.toDate()
      const fiveMinutesAgo = new Date()
      fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5)

      if (lastResent > fiveMinutesAgo) {
        return {
          canResend: false,
          error: "This invitation can only be resent once every 5 minutes",
          nextResendDate: new Date(lastResent.getTime() + 5 * 60 * 1000),
        }
      }
    }

    return { canResend: true }
  } catch (error) {
    console.error("Error checking if invitation can be resent:", error)
    throw error
  }
}

export const getInvitationById = async (invitationId: string) => {
  try {
    const invitationDoc = await getDoc(doc(db, "invitations", invitationId))
    if (!invitationDoc.exists()) {
      return null
    }
    return { ...invitationDoc.data(), id: invitationDoc.id } as Invitation
  } catch (error) {
    console.error("Error getting invitation:", error)
    throw error
  }
}

/**
 * Check if an invitation has expired (older than 1 week)
 */
export const isInvitationExpired = async (invitationId: string): Promise<boolean> => {
  try {
    const invitation = await getInvitationById(invitationId)

    if (!invitation) {
      return true // Consider non-existent invitations as expired
    }

    // If the invitation is already accepted or rejected, it's not expired
    if (invitation.status !== "pending") {
      return false
    }

    // Check if the invitation is more than 1 week old
    if (invitation.createdAt) {
      const createdDate = invitation.createdAt.toDate()
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)

      return createdDate < oneWeekAgo
    }

    return false
  } catch (error) {
    console.error("Error checking if invitation is expired:", error)
    return true // Default to expired on error for security
  }
}

export const inviteUserToSquad = async (squadId: string, email: string, currentUser: User) => {
  try {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return { success: false, error: "Invalid email format" }
    }

    // Check if the user exists (but don't require it)
    const invitee = await getUserByEmail(email)

    // Check if trying to invite yourself
    if (invitee && invitee.uid === currentUser.uid) {
      return { success: false, error: "You cannot invite yourself to the squad" }
    }

    // If email matches current user's email
    if (email.toLowerCase() === currentUser.email?.toLowerCase()) {
      return { success: false, error: "You cannot invite yourself to the squad" }
    }

    // Get the current squad
    const squad = await getSquad(squadId)

    if (!squad) {
      return { success: false, error: "Squad not found" }
    }

    // Check if user is already a member (if they exist)
    if (invitee && squad.members.includes(invitee.uid)) {
      return { success: false, error: "User is already a member of this squad" }
    }

    // Check if there's already a pending invitation for this email
    const existingInvitations = await getSquadInvitations(squadId)
    const pendingInvitation = existingInvitations.find(
      (inv) => inv.inviteeEmail.toLowerCase() === email.toLowerCase() && inv.status === "pending"
    )

    if (pendingInvitation) {
      return { success: false, error: "This email already has a pending invitation" }
    }

    // Create an invitation
    const invitationId = await createInvitation({
      squadId,
      squadName: squad.name,
      inviterId: currentUser.uid,
      inviterName: currentUser.displayName || currentUser.email,
      inviteeId: invitee ? invitee.uid : "", // Use empty string if user doesn't exist
      inviteeEmail: email,
    })

    return { success: true, user: invitee, invitationId }
  } catch (error: any) {
    console.error("Error inviting user to squad:", error)
    return { success: false, error: error.message }
  }
}
