import { BaseRealtimeService } from "../base/base.realtime.service"
import { UserTrip } from "./user-trip.types"
import { User } from "../user/user.types"
import { UserService } from "../user/user.service"

/**
 * UserTrip real-time service for Firebase real-time operations
 */
export class UserTripRealtimeService {
  private static readonly COLLECTION = "userTrips"

  /**
   * Subscribe to a user-trip by user ID and trip ID
   * @param userId User ID
   * @param tripId Trip ID
   * @param callback Callback function to handle user-trip changes
   * @returns Unsubscribe function
   */
  static subscribeToUserTripStatus(
    userId: string,
    tripId: string,
    callback: (userTrip: UserTrip | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<UserTrip>(
      this.COLLECTION,
      [
        ["userId", "==", userId],
        ["tripId", "==", tripId],
      ],
      (userTrips, error) => {
        if (error) {
          callback(null, error)
          return
        }

        if (userTrips.length > 0) {
          callback(userTrips[0])
        } else {
          callback(null)
        }
      }
    )
  }

  /**
   * Subscribe to trip attendees
   * @param tripId Trip ID
   * @param callback Callback function to handle trip attendees changes
   * @returns Unsubscribe function
   */
  static subscribeToTripAttendees(
    tripId: string,
    callback: (attendees: UserTrip[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<UserTrip>(
      this.COLLECTION,
      [
        ["tripId", "==", tripId],
        ["status", "==", "going"],
      ],
      (attendees, error) => {
        if (error) {
          callback([], error)
          return
        }

        callback(attendees)
      }
    )
  }

  /**
   * Subscribe to user's attending trips
   * @param userId User ID
   * @param callback Callback function to handle user's attending trips changes
   * @returns Unsubscribe function
   */
  static subscribeToUserAttendingTrips(
    userId: string,
    callback: (tripIds: string[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<UserTrip>(
      this.COLLECTION,
      [
        ["userId", "==", userId],
        ["status", "==", "going"],
      ],
      (userTrips, error) => {
        if (error) {
          callback([], error)
          return
        }

        const tripIds = userTrips.map((userTrip) => userTrip.tripId)
        callback(tripIds)
      }
    )
  }

  /**
   * Subscribe to trip attendees with user details
   * @param tripId Trip ID
   * @param callback Callback function to handle trip attendees changes
   * @returns Unsubscribe function
   */
  static subscribeToTripAttendeesWithDetails(
    tripId: string,
    callback: (attendees: (UserTrip & { user: User })[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<UserTrip>(
      this.COLLECTION,
      [
        ["tripId", "==", tripId],
        ["status", "==", "going"],
      ],
      async (userTrips, error) => {
        if (error) {
          callback([], error)
          return
        }

        try {
          // Get all user IDs
          const userIds = userTrips.map((userTrip) => userTrip.userId)

          // If no attendees, return empty array
          if (userIds.length === 0) {
            callback([])
            return
          }

          // Get user details for all attendees in a single batch
          const users = await UserService.getUsersFromIds(userIds)

          // Map user details to user trips
          const attendeesWithDetails = userTrips.map((userTrip) => {
            const user = users.find((u) => u.uid === userTrip.userId) as User
            return { ...userTrip, user }
          })

          callback(attendeesWithDetails)
        } catch (err) {
          console.error("Error getting user details for attendees:", err)
          callback(userTrips.map((trip) => ({ ...trip, user: { uid: trip.userId } as User })))
        }
      }
    )
  }
}
