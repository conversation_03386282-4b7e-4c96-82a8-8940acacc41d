import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { UserTrip, UserTripStatus } from "./user-trip.types"
import { User } from "../user/user.types"
import { UserService } from "../user/user.service"

/**
 * User-Trip service for Firebase operations
 */
export class UserTripService {
  private static readonly COLLECTION = "userTrips"

  /**
   * Check a user's status for a trip
   * @param userId User ID
   * @param tripId Trip ID
   * @returns The user trip data or a new default status
   */
  static async checkUserTripStatus(userId: string, tripId: string): Promise<UserTrip> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where("userId", "==", userId),
        where("tripId", "==", tripId)
      )

      const querySnapshot = await getDocs(q)

      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0]
        return { ...doc.data(), id: doc.id } as UserTrip
      }

      // If no status is found, create a default "not-going" status
      const userTripRef = doc(collection(db, this.COLLECTION))
      const userTripId = userTripRef.id

      const newUserTrip: UserTrip = {
        id: userTripId,
        userId,
        tripId,
        status: "not-going",
        createdAt: serverTimestamp() as any,
        updatedAt: serverTimestamp() as any,
      }

      await setDoc(userTripRef, newUserTrip)
      return newUserTrip
    } catch (error) {
      console.error("Error getting user trip status:", error)
      throw error
    }
  }

  /**
   * Get all users who are attending a trip (status = "going")
   * @param tripId Trip ID
   * @returns Array of user IDs
   */
  static async getTripAttendees(tripId: string): Promise<string[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where("tripId", "==", tripId),
        where("status", "==", "going")
      )

      const querySnapshot = await getDocs(q)

      // Return array of user IDs who are attending
      return querySnapshot.docs.map((doc) => doc.data().userId)
    } catch (error) {
      console.error("Error getting trip attendees:", error)
      return []
    }
  }

  /**
   * Synchronize the trip's attendees array with the actual attendees from userTrips
   * @param tripId Trip ID
   * @returns True if successful
   */
  static async syncTripAttendees(tripId: string): Promise<boolean> {
    try {
      // Get all users with "going" status
      const attendees = await this.getTripAttendees(tripId)

      // Update the trip document with the correct attendees
      const tripRef = doc(db, "trips", tripId)
      await updateDoc(tripRef, {
        attendees,
        updatedAt: serverTimestamp(),
      })

      return true
    } catch (error) {
      console.error("Error syncing trip attendees:", error)
      return false
    }
  }

  /**
   * Update a user's trip status
   * @param userId User ID
   * @param tripId Trip ID
   * @param status New status
   * @returns Service response with the user trip ID
   */
  static async updateUserTripStatus(
    userId: string,
    tripId: string,
    status: UserTripStatus
  ): Promise<ServiceResponse> {
    try {
      // Check if user trip status exists
      const existingStatus = await this.checkUserTripStatus(userId, tripId)
      let userTripId = ""

      if (existingStatus) {
        userTripId = existingStatus.id
        await updateDoc(doc(db, this.COLLECTION, userTripId), {
          status,
          updatedAt: serverTimestamp(),
        })
      } else {
        // If no status exists, create a new one
        const userTripRef = doc(collection(db, this.COLLECTION))
        userTripId = userTripRef.id

        await setDoc(userTripRef, {
          id: userTripId,
          userId,
          tripId,
          status,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })
      }

      // REMOVED: syncTripAttendees call to prevent potential infinite loops
      // The trip attendees are now handled by the realtime service when needed
      // await this.syncTripAttendees(tripId)

      return { success: true, id: userTripId }
    } catch (error) {
      console.error("Error updating user trip status:", error)
      return { success: false, error }
    }
  }

  /**
   * Get all trips a user is attending
   * @param userId User ID
   * @returns Array of trip IDs
   */
  static async getUserAttendingTrips(userId: string): Promise<string[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where("userId", "==", userId),
        where("status", "==", "going")
      )

      const querySnapshot = await getDocs(q)

      // Return array of trip IDs the user is attending
      return querySnapshot.docs.map((doc) => doc.data().tripId)
    } catch (error) {
      console.error("Error getting user attending trips:", error)
      return []
    }
  }

  /**
   * Get all users who are attending a trip with their user details
   * @param tripId Trip ID
   * @returns Array of UserTrip objects with user details
   */
  static async getTripAttendeesWithDetails(tripId: string): Promise<(UserTrip & { user: User })[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where("tripId", "==", tripId),
        where("status", "==", "going")
      )

      const querySnapshot = await getDocs(q)

      // Get all user IDs
      const userIds = querySnapshot.docs.map((doc) => doc.data().userId)

      // If no attendees, return empty array
      if (userIds.length === 0) {
        return []
      }

      // Get user details for all attendees in a single batch
      const users = await UserService.getUsersFromIds(userIds)

      // Map user details to user trips
      return querySnapshot.docs.map((doc) => {
        const userTrip = { ...doc.data(), id: doc.id } as UserTrip
        const user = users.find((u) => u.uid === userTrip.userId) as User
        return { ...userTrip, user }
      })
    } catch (error) {
      console.error("Error getting trip attendees with details:", error)
      return []
    }
  }
}
