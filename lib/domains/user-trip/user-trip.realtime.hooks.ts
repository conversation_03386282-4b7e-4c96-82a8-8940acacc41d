"use client"

import { useEffect, useState } from "react"
import { UserTrip, UserTripStatus } from "./user-trip.types"
import { UserTripRealtimeService } from "./user-trip.realtime.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { User } from "../user/user.types"

/**
 * Hook to get real-time updates for a user's trip status
 */
export const useRealtimeUserTripStatus = (tripId: string) => {
  const user = useUser()
  const [userTrip, setUserTrip] = useState<UserTrip | null>(null)
  const [status, setStatus] = useState<UserTripStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user?.uid || !tripId) {
      setUserTrip(null)
      setStatus(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = UserTripRealtimeService.subscribeToUserTripStatus(
      user.uid,
      tripId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time user trip status:", err)
          setError(err)
          setLoading(false)
          return
        }

        setUserTrip(data)
        setStatus(data?.status || "undecided")
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [user, tripId])

  return { userTrip, status, loading, error }
}

/**
 * Hook to get real-time updates for trip attendees
 */
export const useRealtimeTripAttendees = (tripId: string) => {
  const [attendees, setAttendees] = useState<UserTrip[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!tripId) {
      setAttendees([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = UserTripRealtimeService.subscribeToTripAttendees(tripId, (data, err) => {
      if (err) {
        console.error("Error getting real-time trip attendees:", err)
        setError(err)
        setLoading(false)
        return
      }

      setAttendees(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [tripId])

  return { attendees, loading, error }
}

/**
 * Hook to get real-time updates for user's attending trips
 */
export const useRealtimeUserAttendingTrips = () => {
  const user = useUser()
  const [tripIds, setTripIds] = useState<string[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!user?.uid) {
      setTripIds([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = UserTripRealtimeService.subscribeToUserAttendingTrips(
      user.uid,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time user attending trips:", err)
          setError(err)
          setLoading(false)
          return
        }

        setTripIds(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [user])

  return { tripIds, loading, error }
}

/**
 * Hook to get real-time updates for trip attendees with user details
 */
export const useRealtimeTripAttendeesWithDetails = (tripId: string) => {
  const [attendeesWithDetails, setAttendeesWithDetails] = useState<(UserTrip & { user: User })[]>(
    []
  )
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!tripId) {
      setAttendeesWithDetails([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = UserTripRealtimeService.subscribeToTripAttendeesWithDetails(
      tripId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time trip attendees with details:", err)
          setError(err)
          setLoading(false)
          return
        }

        setAttendeesWithDetails(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [tripId])

  return { attendeesWithDetails, loading, error }
}
