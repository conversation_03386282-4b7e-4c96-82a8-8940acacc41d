import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Subscription status type
 */
export type SubscriptionStatus =
  | "active"
  | "canceled"
  | "past_due"
  | "trialing"
  | "incomplete"
  | null

/**
 * Subscription plan type
 */
export type SubscriptionPlan = "free" | "monthly" | "yearly"

/**
 * User subscription entity
 */
export interface UserSubscription extends BaseEntity {
  userId: string
  stripeCustomerId: string
  subscriptionId: string
  subscriptionStatus: SubscriptionStatus
  subscriptionPlan: SubscriptionPlan
  subscriptionCurrentPeriodEnd: Timestamp | number | null
}

/**
 * User subscription creation data
 */
export type UserSubscriptionCreateData = Omit<UserSubscription, "id" | "createdAt" | "updatedAt">

/**
 * User subscription update data
 */
export type UserSubscriptionUpdateData = Partial<
  Omit<UserSubscription, "id" | "userId" | "createdAt">
>

/**
 * User subscriptions map
 * Maps user IDs to subscription status (true if subscribed)
 */
export interface UserSubscriptionsMap {
  [userId: string]: boolean
}
