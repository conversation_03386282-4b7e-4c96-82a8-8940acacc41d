import { BaseRealtimeService } from "../base/base.realtime.service"
import { UserSubscription } from "./user-subscription.types"
import { UserSubscriptionService } from "./user-subscription.service"

/**
 * User subscription real-time service for Firebase real-time operations
 */
export class UserSubscriptionRealtimeService {
  private static readonly COLLECTION = "userSubscriptions"

  /**
   * Subscribe to a user subscription by user ID
   * @param userId User ID
   * @param callback Callback function to handle subscription changes
   * @returns Unsubscribe function
   */
  static subscribeToUserSubscription(
    userId: string,
    callback: (subscription: UserSubscription | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<UserSubscription>(
      this.COLLECTION,
      userId,
      async (data, error) => {
        if (error) {
          callback(null, error)
          return
        }

        if (data) {
          callback(data)
        } else {
          // If no subscription document exists, create a default free subscription
          try {
            const defaultSubscription = await UserSubscriptionService.getUserSubscription(userId)
            callback(defaultSubscription)
          } catch (err) {
            console.error("Error creating default subscription:", err)
            callback(null, err as Error)
          }
        }
      }
    )
  }

  /**
   * Subscribe to all user subscriptions
   * @param callback Callback function to handle subscriptions changes
   * @returns Unsubscribe function
   */
  static subscribeToAllUserSubscriptions(
    callback: (subscriptions: UserSubscription[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToCollection<UserSubscription>(this.COLLECTION, callback)
  }

  /**
   * Subscribe to multiple user subscriptions by user IDs
   * @param userIds Array of user IDs
   * @param callback Callback function to handle subscriptions map
   * @returns Unsubscribe function
   */
  static subscribeToUserSubscriptions(
    userIds: string[],
    callback: (subscriptionsMap: Record<string, boolean>, error?: Error) => void
  ): () => void {
    if (!userIds.length) {
      callback({})
      return () => {}
    }

    // Create a map to track subscriptions
    const subscriptionsMap: Record<string, boolean> = {}
    const unsubscribeFunctions: (() => void)[] = []

    // Subscribe to each user's subscription
    userIds.forEach((userId) => {
      const unsubscribe = this.subscribeToUserSubscription(userId, (subscription, error) => {
        if (error) {
          console.error(`Error getting subscription for user ${userId}:`, error)
          subscriptionsMap[userId] = false
        } else {
          // Check if the user has an active subscription
          const isSubscribed =
            subscription?.subscriptionStatus === "active" &&
            (subscription?.subscriptionPlan === "monthly" ||
              subscription?.subscriptionPlan === "yearly")

          subscriptionsMap[userId] = isSubscribed
        }

        // Call the callback with the updated map
        callback({ ...subscriptionsMap })
      })

      unsubscribeFunctions.push(unsubscribe)
    })

    // Return a function that unsubscribes from all subscriptions
    return () => {
      unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
    }
  }
}
