import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Trip status type
 */
export type TripStatus = "planning" | "upcoming" | "active" | "completed" | "cancelled"

/**
 * Trip image attribution
 */
export interface TripImageAttribution {
  name: string
  photoReference?: string
  username?: string
  link?: string
}

/**
 * Trip entity
 */
export interface Trip extends BaseEntity {
  name: string
  destination: string
  squadId: string
  startDate: Timestamp
  endDate: Timestamp
  budget: number
  description?: string
  image?: string
  locationThumbnail?: string
  imageAttribution?: TripImageAttribution
  status: TripStatus
  attendees: string[] // Array of user IDs with "going" status
  leaderId: string // User ID of the trip leader
  tasksCompleted: number
  totalTasks: number
  createdBy: string // User ID
}

/**
 * Trip creation data
 */
export type TripCreateData = Omit<Trip, "id" | "createdAt" | "tasksCompleted" | "totalTasks">

/**
 * Trip update data - excludes destination to prevent changes
 */
export type TripUpdateData = Partial<Omit<Trip, "destination">>
// export type TripUpdateData = Partial<Trip>

export interface TripFormData {
  name: string
  destination: string
  placeId?: string
  squadId?: string
  startDate: Date | null
  endDate: Date | null
  budget: number
  description: string
}
