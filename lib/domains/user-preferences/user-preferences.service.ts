import { db } from "@/lib/firebase"
import { doc, getDoc, setDoc, updateDoc, serverTimestamp, Timestamp } from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { UserService } from "../user/user.service"
import {
  UserPreferences,
  UserPreferencesCreateData,
  UserPreferencesUpdateData,
} from "./user-preferences.types"

/**
 * User preferences service for Firebase operations
 */
export class UserPreferencesService {
  private static readonly COLLECTION = "userPreferences"

  /**
   * Get user preferences by user ID
   * @param userId User ID
   * @returns The user preferences or null if not found
   */
  static async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    try {
      const preferencesDoc = await getDoc(doc(db, this.COLLECTION, userId))

      if (preferencesDoc.exists()) {
        return { ...preferencesDoc.data(), id: userId } as UserPreferences
      }

      // If no preferences document exists, create a default one
      const defaultPreferences = await this.createDefaultPreferences(userId)
      return defaultPreferences
    } catch (error) {
      console.error("Error getting user preferences:", error)
      throw error
    }
  }

  /**
   * Create default preferences for a user
   * @param userId User ID
   * @returns The created user preferences
   */
  private static async createDefaultPreferences(userId: string): Promise<UserPreferences> {
    try {
      const defaultPreferences: UserPreferencesCreateData = {
        userId,
        theme: "system",
        travelPreferences: [],
        budgetRange: [500, 2000],
        availabilityPreferences: [],
        preferredTravelSeasons: [],
        travelGroupPreferences: [],
        // AI preferences
        aiEnabled: true,
        proactiveSuggestions: true,
        // Notification preferences
        notificationsEnabled: true,
        emailNotifications: true,
        pushNotifications: true,
        tripUpdatesNotifications: true,
        squadMessagesNotifications: true,
        invitationNotifications: true,
        aiSuggestionsNotifications: true,
      }

      const preferencesRef = doc(db, this.COLLECTION, userId)

      await setDoc(preferencesRef, {
        ...defaultPreferences,
        id: userId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return {
        ...defaultPreferences,
        id: userId,
        createdAt: null,
        updatedAt: null,
      }
    } catch (error) {
      console.error("Error creating default user preferences:", error)
      throw error
    }
  }

  /**
   * Update user preferences
   * @param userId User ID
   * @param preferencesData User preferences data to update
   * @returns Service response indicating success or failure
   */
  static async updateUserPreferences(
    userId: string,
    preferencesData: UserPreferencesUpdateData
  ): Promise<ServiceResponse> {
    try {
      const preferencesRef = doc(db, this.COLLECTION, userId)
      const preferencesDoc = await getDoc(preferencesRef)

      if (preferencesDoc.exists()) {
        // Update existing preferences
        await updateDoc(preferencesRef, {
          ...preferencesData,
          updatedAt: serverTimestamp(),
        })
      } else {
        // Create new preferences with defaults and overrides
        const defaultPreferences: UserPreferencesCreateData = {
          userId,
          theme: preferencesData.theme || "system",
          travelPreferences: preferencesData.travelPreferences || [],
          budgetRange: preferencesData.budgetRange || [500, 2000],
          availabilityPreferences: preferencesData.availabilityPreferences || [],
          preferredTravelSeasons: preferencesData.preferredTravelSeasons || [],
          travelGroupPreferences: preferencesData.travelGroupPreferences || [],
          // AI preferences
          aiEnabled: preferencesData.aiEnabled !== undefined ? preferencesData.aiEnabled : true,
          proactiveSuggestions:
            preferencesData.proactiveSuggestions !== undefined
              ? preferencesData.proactiveSuggestions
              : true,
          // Notification preferences
          notificationsEnabled:
            preferencesData.notificationsEnabled !== undefined
              ? preferencesData.notificationsEnabled
              : true,
          emailNotifications:
            preferencesData.emailNotifications !== undefined
              ? preferencesData.emailNotifications
              : true,
          pushNotifications:
            preferencesData.pushNotifications !== undefined
              ? preferencesData.pushNotifications
              : true,
          tripUpdatesNotifications:
            preferencesData.tripUpdatesNotifications !== undefined
              ? preferencesData.tripUpdatesNotifications
              : true,
          squadMessagesNotifications:
            preferencesData.squadMessagesNotifications !== undefined
              ? preferencesData.squadMessagesNotifications
              : true,
          invitationNotifications:
            preferencesData.invitationNotifications !== undefined
              ? preferencesData.invitationNotifications
              : true,
          aiSuggestionsNotifications:
            preferencesData.aiSuggestionsNotifications !== undefined
              ? preferencesData.aiSuggestionsNotifications
              : true,
        }

        await setDoc(preferencesRef, {
          ...defaultPreferences,
          id: userId,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })
      }

      return { success: true }
    } catch (error) {
      console.error("Error updating user preferences:", error)
      return { success: false, error }
    }
  }

  /**
   * Update user theme preference
   * @param userId User ID
   * @param theme Theme preference
   * @returns Service response indicating success or failure
   */
  static async updateUserTheme(
    userId: string,
    theme: "light" | "dark" | "system"
  ): Promise<ServiceResponse> {
    return this.updateUserPreferences(userId, { theme })
  }
}
