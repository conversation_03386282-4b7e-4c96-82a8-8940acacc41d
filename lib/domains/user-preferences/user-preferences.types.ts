import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * User preferences entity
 */
export interface UserPreferences extends BaseEntity {
  userId: string
  theme: "light" | "dark" | "system"
  travelPreferences: string[]
  budgetRange: [number, number] | string
  availabilityPreferences: string[]
  preferredTravelSeasons: string[]
  travelGroupPreferences: string[]
  // AI preferences
  aiEnabled: boolean
  proactiveSuggestions: boolean
  // Notification preferences
  notificationsEnabled: boolean
  emailNotifications: boolean
  pushNotifications: boolean
  tripUpdatesNotifications: boolean
  squadMessagesNotifications: boolean
  invitationNotifications: boolean
  aiSuggestionsNotifications: boolean
  location: string
  locationPlaceId: string
}

/**
 * User preferences creation data
 */
export type UserPreferencesCreateData = Omit<UserPreferences, "id" | "createdAt" | "updatedAt">

/**
 * User preferences update data
 */
export type UserPreferencesUpdateData = Partial<UserPreferences>
