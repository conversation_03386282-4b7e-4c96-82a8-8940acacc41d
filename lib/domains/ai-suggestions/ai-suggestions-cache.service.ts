"use client"

import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"

// Define the localStorage key prefix
const AI_SUGGESTIONS_CACHE_KEY_PREFIX = "togeda-ai-suggestions"

// Types for cached suggestions
export interface CachedTaskSuggestion {
  title: string
  description: string
  category?: string
  priority?: string
  tags?: string[]
  affiliateLinks?: Array<{
    url: string
    title: string
    description: string
    provider?: string
    tags?: string[]
  }>
  // Legacy support for single affiliate link (will be deprecated)
  affiliateLink?: {
    url: string
    title: string
    description: string
    provider?: string
    tags?: string[]
  } | null
}

export interface CachedTripSuggestion {
  destination: string
  description: string
  tags: string[]
  budget: string
  image: string
  placeId?: string
  formattedAddress?: string
  attribution?: {
    name: string
    photoReference?: string
    username?: string
    link?: string
  }
}

export interface CachedItinerarySuggestion {
  title: string
  description: string
  day: number
  timeOfDay: "morning" | "afternoon" | "evening"
  duration: string
  tags?: string[]
  affiliateLinks?: Array<{
    url: string
    title: string
    description: string
    provider?: string
    tags?: string[]
  }>
  // Legacy support for single affiliate link (will be deprecated)
  affiliateLink?: {
    url: string
    title: string
    description: string
    provider?: string
    tags?: string[]
  } | null
}

// Type for the cache entry
interface CacheEntry<T> {
  data: T[]
  timestamp: number
  contextId: string // tripId or squadId
  userId: string
}

/**
 * AI Suggestions Cache Service
 * Handles caching AI-generated suggestions in localStorage
 */
export class AISuggestionsCacheService {
  /**
   * Generate a cache key for a specific category and context
   */
  private static getCacheKey(category: AIUsageCategory, contextId: string): string {
    return `${AI_SUGGESTIONS_CACHE_KEY_PREFIX}-${category}-${contextId}`
  }

  /**
   * Save suggestions to localStorage
   */
  static saveSuggestions<T>(
    category: AIUsageCategory,
    contextId: string,
    userId: string,
    suggestions: T[]
  ): void {
    if (typeof window === "undefined") return

    const cacheKey = this.getCacheKey(category, contextId)
    const cacheEntry: CacheEntry<T> = {
      data: suggestions,
      timestamp: Date.now(),
      contextId,
      userId,
    }

    try {
      localStorage.setItem(cacheKey, JSON.stringify(cacheEntry))
    } catch (error) {
      console.error("Error saving suggestions to localStorage:", error)
    }
  }

  /**
   * Get suggestions from localStorage
   */
  static getSuggestions<T>(
    category: AIUsageCategory,
    contextId: string,
    userId: string
  ): T[] | null {
    if (typeof window === "undefined") return null

    const cacheKey = this.getCacheKey(category, contextId)

    try {
      const cachedData = localStorage.getItem(cacheKey)
      if (!cachedData) return null

      const cacheEntry = JSON.parse(cachedData) as CacheEntry<T>

      // Only return suggestions if they belong to the same user
      if (cacheEntry.userId !== userId) return null

      // Only return suggestions if they're for the same context
      if (cacheEntry.contextId !== contextId) return null

      return cacheEntry.data
    } catch (error) {
      console.error("Error retrieving suggestions from localStorage:", error)
      return null
    }
  }

  /**
   * Check if cached suggestions exist
   */
  static hasCachedSuggestions(
    category: AIUsageCategory,
    contextId: string,
    userId: string
  ): boolean {
    if (typeof window === "undefined") return false

    const cacheKey = this.getCacheKey(category, contextId)

    try {
      const cachedData = localStorage.getItem(cacheKey)
      if (!cachedData) return false

      const cacheEntry = JSON.parse(cachedData) as CacheEntry<any>

      // Only consider suggestions if they belong to the same user
      if (cacheEntry.userId !== userId) return false

      // Only consider suggestions if they're for the same context
      if (cacheEntry.contextId !== contextId) return false

      return Array.isArray(cacheEntry.data) && cacheEntry.data.length > 0
    } catch (error) {
      console.error("Error checking for cached suggestions:", error)
      return false
    }
  }

  /**
   * Clear cached suggestions
   */
  static clearSuggestions(category: AIUsageCategory, contextId: string): void {
    if (typeof window === "undefined") return

    const cacheKey = this.getCacheKey(category, contextId)

    try {
      localStorage.removeItem(cacheKey)
    } catch (error) {
      console.error("Error clearing cached suggestions:", error)
    }
  }

  /**
   * Clear all cached suggestions
   */
  static clearAllSuggestions(): void {
    if (typeof window === "undefined") return

    try {
      Object.keys(localStorage).forEach((key) => {
        if (key.startsWith(AI_SUGGESTIONS_CACHE_KEY_PREFIX)) {
          localStorage.removeItem(key)
        }
      })
    } catch (error) {
      console.error("Error clearing all cached suggestions:", error)
    }
  }
}
