"use client"

import { useState, useCallback } from "react"
import { AIUsageCategory } from "../user-ai-usage/user-ai-usage.types"
import {
  AISuggestionsCacheService,
  CachedTaskSuggestion,
  CachedTripSuggestion,
  CachedItinerarySuggestion,
} from "./ai-suggestions-cache.service"

/**
 * Hook for using the AI suggestions cache
 */
export function useAISuggestionsCache() {
  const [loading, setLoading] = useState(false)

  /**
   * Save task suggestions to cache
   */
  const saveTaskSuggestions = useCallback(
    (tripId: string, userId: string, suggestions: CachedTaskSuggestion[]) => {
      try {
        AISuggestionsCacheService.saveSuggestions<CachedTaskSuggestion>(
          AIUsageCategory.TASK,
          tripId,
          userId,
          suggestions
        )
        return true
      } catch (error) {
        console.error("Error saving task suggestions to cache:", error)
        return false
      }
    },
    []
  )

  /**
   * Get task suggestions from cache
   */
  const getTaskSuggestions = useCallback(
    (tripId: string, userId: string): CachedTaskSuggestion[] | null => {
      try {
        setLoading(true)
        return AISuggestionsCacheService.getSuggestions<CachedTaskSuggestion>(
          AIUsageCategory.TASK,
          tripId,
          userId
        )
      } catch (error) {
        console.error("Error getting task suggestions from cache:", error)
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Save trip suggestions to cache
   */
  const saveTripSuggestions = useCallback(
    (squadId: string, userId: string, suggestions: CachedTripSuggestion[]) => {
      try {
        AISuggestionsCacheService.saveSuggestions<CachedTripSuggestion>(
          AIUsageCategory.TRIP,
          squadId,
          userId,
          suggestions
        )
        return true
      } catch (error) {
        console.error("Error saving trip suggestions to cache:", error)
        return false
      }
    },
    []
  )

  /**
   * Get trip suggestions from cache
   */
  const getTripSuggestions = useCallback(
    (squadId: string, userId: string): CachedTripSuggestion[] | null => {
      try {
        setLoading(true)
        return AISuggestionsCacheService.getSuggestions<CachedTripSuggestion>(
          AIUsageCategory.TRIP,
          squadId,
          userId
        )
      } catch (error) {
        console.error("Error getting trip suggestions from cache:", error)
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Save itinerary suggestions to cache
   */
  const saveItinerarySuggestions = useCallback(
    (tripId: string, userId: string, suggestions: CachedItinerarySuggestion[]) => {
      try {
        // Use just the tripId as the context ID to make suggestions available for all days
        AISuggestionsCacheService.saveSuggestions<CachedItinerarySuggestion>(
          AIUsageCategory.ITINERARY,
          tripId,
          userId,
          suggestions
        )
        return true
      } catch (error) {
        console.error("Error saving itinerary suggestions to cache:", error)
        return false
      }
    },
    []
  )

  /**
   * Get itinerary suggestions from cache
   */
  const getItinerarySuggestions = useCallback(
    (tripId: string, userId: string): CachedItinerarySuggestion[] | null => {
      try {
        setLoading(true)
        // Use just the tripId as the context ID to make suggestions available for all days
        return AISuggestionsCacheService.getSuggestions<CachedItinerarySuggestion>(
          AIUsageCategory.ITINERARY,
          tripId,
          userId
        )
      } catch (error) {
        console.error("Error getting itinerary suggestions from cache:", error)
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Check if cached suggestions exist
   */
  const hasCachedSuggestions = useCallback(
    (category: AIUsageCategory, contextId: string, userId: string): boolean => {
      try {
        // Use contextId directly for all categories
        return AISuggestionsCacheService.hasCachedSuggestions(category, contextId, userId)
      } catch (error) {
        console.error("Error checking for cached suggestions:", error)
        return false
      }
    },
    []
  )

  /**
   * Clear cached suggestions
   */
  const clearCachedSuggestions = useCallback(
    (category: AIUsageCategory, contextId: string): void => {
      try {
        // Use contextId directly for all categories
        AISuggestionsCacheService.clearSuggestions(category, contextId)
      } catch (error) {
        console.error("Error clearing cached suggestions:", error)
      }
    },
    []
  )

  return {
    loading,
    saveTaskSuggestions,
    getTaskSuggestions,
    saveTripSuggestions,
    getTripSuggestions,
    saveItinerarySuggestions,
    getItinerarySuggestions,
    hasCachedSuggestions,
    clearCachedSuggestions,
  }
}
