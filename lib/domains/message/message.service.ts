import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  setDoc,
  deleteDoc,
  DocumentSnapshot,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { Message, MessageCreateData, MessageUpdateData } from "./message.types"

/**
 * Message service for Firebase operations
 */
export class MessageService {
  private static readonly COLLECTION = "trips"
  private static readonly SUBCOLLECTION = "messages"

  /**
   * Create a new message
   * @param tripId Trip ID
   * @param messageData Message data
   * @returns The new message ID
   */
  static async createMessage(tripId: string, messageData: MessageCreateData): Promise<string> {
    try {
      const messageRef = doc(collection(db, this.COLLECTION, tripId, this.SUBCOLLECTION))
      const messageId = messageRef.id

      // Sanitize content
      const sanitizedContent = this.sanitizeContent(messageData.content)

      // Extract mentioned user IDs from content
      const mentionedUserIds = this.extractMentionedUserIds(sanitizedContent)

      await setDoc(messageRef, {
        ...messageData,
        id: messageId,
        content: sanitizedContent,
        mentionedUserIds,
        createdAt: serverTimestamp(),
      })

      return messageId
    } catch (error) {
      console.error("Error creating message:", error)
      throw error
    }
  }

  /**
   * Get messages for a trip with pagination
   * @param tripId Trip ID
   * @param limitCount Number of messages to fetch
   * @param lastDoc Last document for pagination
   * @returns Messages and last document
   */
  static async getMessages(
    tripId: string,
    limitCount: number = 20,
    lastDoc?: DocumentSnapshot
  ): Promise<{ messages: Message[]; lastDoc: DocumentSnapshot | null }> {
    try {
      let q = query(
        collection(db, this.COLLECTION, tripId, this.SUBCOLLECTION),
        orderBy("createdAt", "desc"),
        limit(limitCount)
      )

      if (lastDoc) {
        q = query(q, startAfter(lastDoc))
      }

      const snapshot = await getDocs(q)
      const messages: Message[] = []
      let newLastDoc: DocumentSnapshot | null = null

      snapshot.forEach((doc) => {
        const data = doc.data()
        messages.push({
          id: doc.id,
          ...data,
        } as Message)
      })

      if (snapshot.docs.length > 0) {
        newLastDoc = snapshot.docs[snapshot.docs.length - 1]
      }

      return { messages, lastDoc: newLastDoc }
    } catch (error) {
      console.error("Error getting messages:", error)
      throw error
    }
  }

  /**
   * Get a single message by ID
   * @param tripId Trip ID
   * @param messageId Message ID
   * @returns The message or null if not found
   */
  static async getMessage(tripId: string, messageId: string): Promise<Message | null> {
    try {
      const messageRef = doc(db, this.COLLECTION, tripId, this.SUBCOLLECTION, messageId)
      const messageSnap = await getDoc(messageRef)

      if (messageSnap.exists()) {
        return {
          id: messageSnap.id,
          ...messageSnap.data(),
        } as Message
      }

      return null
    } catch (error) {
      console.error("Error getting message:", error)
      throw error
    }
  }

  /**
   * Update a message
   * @param tripId Trip ID
   * @param messageId Message ID
   * @param updateData Update data
   * @returns Service response
   */
  static async updateMessage(
    tripId: string,
    messageId: string,
    updateData: MessageUpdateData
  ): Promise<ServiceResponse> {
    try {
      const messageRef = doc(db, this.COLLECTION, tripId, this.SUBCOLLECTION, messageId)

      const sanitizedData = { ...updateData }
      if (updateData.content) {
        sanitizedData.content = this.sanitizeContent(updateData.content)
      }

      await updateDoc(messageRef, {
        ...sanitizedData,
        updatedAt: serverTimestamp(),
      })

      return { success: true, id: messageId }
    } catch (error) {
      console.error("Error updating message:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete a message
   * @param tripId Trip ID
   * @param messageId Message ID
   * @returns Service response
   */
  static async deleteMessage(tripId: string, messageId: string): Promise<ServiceResponse> {
    try {
      const messageRef = doc(db, this.COLLECTION, tripId, this.SUBCOLLECTION, messageId)
      await deleteDoc(messageRef)

      return { success: true, id: messageId }
    } catch (error) {
      console.error("Error deleting message:", error)
      return { success: false, error }
    }
  }

  /**
   * Sanitize message content for security
   * @param content Raw content
   * @returns Sanitized content
   */
  private static sanitizeContent(content: string): string {
    // Basic sanitization - remove HTML tags and limit length
    return content
      .replace(/<[^>]*>/g, "") // Remove HTML tags
      .trim()
      .substring(0, 750) // Enforce character limit
  }

  /**
   * Extract mentioned user IDs from message content
   * @param content Message content
   * @returns Array of mentioned user IDs
   */
  private static extractMentionedUserIds(content: string): string[] {
    // Extract @mentions in format @[userId:displayName]
    const mentionRegex = /@\[([^:]+):[^\]]+\]/g
    const mentions: string[] = []
    let match

    while ((match = mentionRegex.exec(content)) !== null) {
      mentions.push(match[1])
    }

    return [...new Set(mentions)] // Remove duplicates
  }
}
