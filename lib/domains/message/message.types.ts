import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Message entity
 */
export interface Message extends BaseEntity {
  tripId: string
  senderId: string
  senderName: string
  senderPhotoURL?: string
  content: string
  mentionedUserIds: string[]
  imageUrl?: string // For future image support
}

/**
 * Message creation data
 */
export type MessageCreateData = Omit<Message, "id" | "createdAt" | "updatedAt">

/**
 * Message update data
 */
export type MessageUpdateData = Partial<Pick<Message, "content">>
