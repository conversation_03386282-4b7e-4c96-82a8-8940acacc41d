import { BaseRealtimeService } from "../base/base.realtime.service"
import { Invitation } from "./invitation.types"

/**
 * Invitation real-time service for Firebase real-time operations
 */
export class InvitationRealtimeService {
  private static readonly COLLECTION = "invitations"

  /**
   * Subscribe to an invitation by ID
   * @param invitationId Invitation ID
   * @param callback Callback function to handle invitation changes
   * @returns Unsubscribe function
   */
  static subscribeToInvitation(
    invitationId: string,
    callback: (invitation: Invitation | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<Invitation>(
      this.COLLECTION,
      invitationId,
      callback
    )
  }

  /**
   * Subscribe to invitations for a squad
   * @param squadId Squad ID
   * @param callback Callback function to handle invitations changes
   * @returns Unsubscribe function
   */
  static subscribeToSquadInvitations(
    squadId: string,
    callback: (invitations: Invitation[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<Invitation>(
      this.COLLECTION,
      [["squadId", "==", squadId]],
      callback
    )
  }

  /**
   * Subscribe to invitations for a user by email
   * @param email User email
   * @param callback Callback function to handle invitations changes
   * @returns Unsubscribe function
   */
  static subscribeToUserInvitations(
    email: string,
    callback: (invitations: Invitation[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<Invitation>(
      this.COLLECTION,
      [["inviteeEmail", "==", email]],
      callback
    )
  }
}
