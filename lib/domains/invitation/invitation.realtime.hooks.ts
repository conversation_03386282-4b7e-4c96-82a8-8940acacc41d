"use client"

import { useEffect, useState } from "react"
import { Invitation } from "./invitation.types"
import { InvitationRealtimeService } from "./invitation.realtime.service"
import { useUser, useUserData } from "@/lib/domains/auth/auth.hooks"

/**
 * Hook to get real-time updates for a specific invitation
 */
export const useRealtimeInvitation = (invitationId: string) => {
  const [invitation, setInvitation] = useState<Invitation | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!invitationId) {
      setInvitation(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToInvitation(
      invitationId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time invitation:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitation(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [invitationId])

  return { invitation, loading, error }
}

/**
 * Hook to get real-time updates for squad invitations
 */
export const useRealtimeSquadInvitations = (squadId: string) => {
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!squadId) {
      setInvitations([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToSquadInvitations(
      squadId,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time squad invitations:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitations(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [squadId])

  return { invitations, loading, error }
}

/**
 * Hook to get real-time updates for user invitations
 */
export const useRealtimeUserInvitations = () => {
  const userData = useUserData()
  const [invitations, setInvitations] = useState<Invitation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userData?.email) {
      setInvitations([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)

    const unsubscribe = InvitationRealtimeService.subscribeToUserInvitations(
      userData.email,
      (data, err) => {
        if (err) {
          console.error("Error getting real-time user invitations:", err)
          setError(err)
          setLoading(false)
          return
        }

        setInvitations(data)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [userData])

  return { invitations, loading, error }
}
