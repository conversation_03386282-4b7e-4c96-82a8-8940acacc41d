import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Invitation status type
 */
export type InvitationStatus = "pending" | "accepted" | "rejected"

/**
 * Invitation entity
 */
export interface Invitation extends BaseEntity {
  squadId: string
  squadName: string
  inviterId: string
  inviterName: string
  inviteeId: string
  inviteeEmail: string
  status: InvitationStatus
  lastUpdated?: Timestamp | null
  lastResent?: Timestamp | null
}

/**
 * Invitation creation data
 */
export type InvitationCreateData = Omit<Invitation, "id" | "createdAt" | "status"> & {
  status?: InvitationStatus
}

/**
 * Invitation update data
 */
export type InvitationUpdateData = Partial<Invitation>
