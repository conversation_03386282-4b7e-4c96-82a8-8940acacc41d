import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  deleteDoc,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  Invitation,
  InvitationCreateData,
  InvitationStatus,
  InvitationUpdateData,
} from "./invitation.types"
import { SquadService } from "../squad/squad.service"

/**
 * Invitation service for Firebase operations
 */
export class InvitationService {
  private static readonly COLLECTION = "invitations"

  /**
   * Create a new invitation
   * @param invitationData Invitation data
   * @returns The new invitation ID
   */
  static async createInvitation(invitationData: InvitationCreateData): Promise<string> {
    try {
      const invitationRef = doc(collection(db, this.COLLECTION))
      const invitationId = invitationRef.id

      await setDoc(invitationRef, {
        ...invitationData,
        id: invitationId,
        status: invitationData.status || "pending",
        createdAt: serverTimestamp(),
      })

      return invitationId
    } catch (error) {
      console.error("Error creating invitation:", error)
      throw error
    }
  }

  /**
   * Get an invitation by ID
   * @param invitationId Invitation ID
   * @returns The invitation data or null if not found
   */
  static async getInvitation(invitationId: string): Promise<Invitation | null> {
    try {
      const invitationDoc = await getDoc(doc(db, this.COLLECTION, invitationId))

      if (invitationDoc.exists()) {
        return { ...invitationDoc.data(), id: invitationId } as Invitation
      }

      return null
    } catch (error) {
      console.error("Error getting invitation:", error)
      throw error
    }
  }

  /**
   * Get invitations for a squad
   * @param squadId Squad ID
   * @returns Array of invitations
   */
  static async getSquadInvitations(squadId: string): Promise<Invitation[]> {
    try {
      const q = query(collection(db, this.COLLECTION), where("squadId", "==", squadId))
      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Invitation)
    } catch (error) {
      console.error("Error getting squad invitations:", error)
      throw error
    }
  }

  /**
   * Get invitations for a user (by invitee email)
   * @param email User email
   * @returns Array of invitations
   */
  static async getUserInvitations(email: string): Promise<Invitation[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where("inviteeEmail", "==", email),
        where("status", "==", "pending")
      )

      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Invitation)
    } catch (error) {
      console.error("Error getting user invitations:", error)
      throw error
    }
  }

  /**
   * Update an invitation
   * @param invitationId Invitation ID
   * @param invitationData Invitation data to update
   * @returns Service response
   */
  static async updateInvitation(
    invitationId: string,
    invitationData: InvitationUpdateData
  ): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, invitationId), {
        ...invitationData,
        lastUpdated: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating invitation:", error)
      return { success: false, error }
    }
  }

  /**
   * Update invitation status
   * @param invitationId Invitation ID
   * @param status New status
   * @param userId User ID (for accepted invitations)
   * @returns Service response
   */
  static async updateInvitationStatus(
    invitationId: string,
    status: InvitationStatus,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      const invitation = await this.getInvitation(invitationId)

      if (!invitation) {
        return { success: false, error: new Error("Invitation not found") }
      }

      // Update invitation status
      await updateDoc(doc(db, this.COLLECTION, invitationId), {
        status,
        lastUpdated: serverTimestamp(),
      })

      // If accepting invitation, add user to squad
      if (status === "accepted" && userId) {
        await SquadService.addMember(invitation.squadId, userId)
      }

      return { success: true }
    } catch (error) {
      console.error("Error updating invitation status:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete an invitation
   * @param invitationId Invitation ID
   * @returns Service response
   */
  static async deleteInvitation(invitationId: string): Promise<ServiceResponse> {
    try {
      await deleteDoc(doc(db, this.COLLECTION, invitationId))
      return { success: true }
    } catch (error) {
      console.error("Error deleting invitation:", error)
      return { success: false, error }
    }
  }

  /**
   * Resend an invitation
   * @param invitationId Invitation ID
   * @returns Service response
   */
  static async resendInvitation(invitationId: string): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, invitationId), {
        lastResent: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error resending invitation:", error)
      return { success: false, error }
    }
  }
}
