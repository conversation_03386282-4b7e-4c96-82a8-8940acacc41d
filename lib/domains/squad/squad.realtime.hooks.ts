"use client"

import { useEffect, useCallback, useState } from "react"
import { SquadRealtimeService } from "./squad.realtime.service"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { useSquadStore } from "./squad.store"

/**
 * Hook to get real-time updates for a specific squad
 */
export const useRealtimeSquad = (squadId: string) => {
  // Use local state for loading and error to avoid shared state issues
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Use store for the squad data
  const currentSquad = useSquadStore((state) => state.currentSquad)
  const setCurrentSquad = useSquadStore((state) => state.setCurrentSquad)

  useEffect(() => {
    if (!squadId) {
      setCurrentSquad(null)
      setLoading(false)
      return () => {}
    }

    setLoading(true)
    setError(null)

    const unsubscribe = SquadRealtimeService.subscribeToSquad(squadId, (data, err) => {
      if (err) {
        console.error("Error getting real-time squad:", err)
        setError(err)
        setLoading(false)
        return
      }

      setCurrentSquad(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [squadId, setCurrentSquad])

  return { squad: currentSquad, loading, error }
}

/**
 * Hook to get real-time updates for user squads
 */
export const useRealtimeUserSquads = () => {
  const user = useUser()

  // Use local state for loading and error to avoid shared state issues
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Use store for the squads data
  const squads = useSquadStore((state) => state.squads)
  const setSquads = useCallback((data: any[]) => {
    useSquadStore.setState({ squads: data })
  }, [])

  useEffect(() => {
    if (!user?.uid) {
      setSquads([])
      setLoading(false)
      return () => {}
    }

    setLoading(true)
    setError(null)

    const unsubscribe = SquadRealtimeService.subscribeToUserSquads(user.uid, (data, err) => {
      if (err) {
        console.error("Error getting real-time user squads:", err)
        setError(err)
        setLoading(false)
        return
      }

      setSquads(data)
      setLoading(false)
    })

    return () => unsubscribe()
  }, [user, setSquads])

  return { squads, loading, error }
}
