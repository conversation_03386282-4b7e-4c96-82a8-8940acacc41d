import { BaseRealtimeService } from "../base/base.realtime.service"
import { Squad } from "./squad.types"

/**
 * Squad real-time service for Firebase real-time operations
 */
export class SquadRealtimeService {
  private static readonly COLLECTION = "squads"

  /**
   * Subscribe to a squad by ID
   * @param squadId Squad ID
   * @param callback Callback function to handle squad changes
   * @returns Unsubscribe function
   */
  static subscribeToSquad(
    squadId: string,
    callback: (squad: Squad | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<Squad>(this.COLLECTION, squadId, callback)
  }

  /**
   * Subscribe to squads for a user
   * @param userId User ID
   * @param callback Callback function to handle squads changes
   * @returns Unsubscribe function
   */
  static subscribeToUserSquads(
    userId: string,
    callback: (squads: Squad[], error?: Error) => void
  ): () => void {
    // Query squads where the user is a member
    return BaseRealtimeService.subscribeToQuery<Squad>(
      this.COLLECTION,
      [["members", "array-contains", userId]],
      callback
    )
  }
}
