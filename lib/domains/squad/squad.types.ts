import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Squad entity
 */
export interface Squad extends BaseEntity {
  name: string
  description?: string
  leaderId: string
  members: string[] // Array of user IDs
}

/**
 * Squad creation data
 */
export type SquadCreateData = Omit<Squad, "id" | "createdAt">

/**
 * Squad update data
 */
export type SquadUpdateData = Partial<Squad>
