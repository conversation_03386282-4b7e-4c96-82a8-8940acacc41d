import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  arrayUnion,
  arrayRemove,
  deleteDoc,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { Squad, SquadCreateData, SquadUpdateData } from "./squad.types"

/**
 * Squad service for Firebase operations
 */
export class SquadService {
  private static readonly COLLECTION = "squads"

  /**
   * Create a new squad
   * @param squadData Squad data
   * @returns The new squad ID
   */
  static async createSquad(squadData: SquadCreateData): Promise<string> {
    try {
      const squadRef = doc(collection(db, this.COLLECTION))
      const squadId = squadRef.id

      await setDoc(squadRef, {
        ...squadData,
        id: squadId,
        createdAt: serverTimestamp(),
      })

      return squadId
    } catch (error) {
      console.error("Error creating squad:", error)
      throw error
    }
  }

  /**
   * Get a squad by ID
   * @param squadId Squad ID
   * @returns The squad data or null if not found
   */
  static async getSquad(squadId: string): Promise<Squad | null> {
    try {
      const squadDoc = await getDoc(doc(db, this.COLLECTION, squadId))

      if (squadDoc.exists()) {
        return { ...squadDoc.data(), id: squadId } as Squad
      }

      return null
    } catch (error) {
      console.error("Error getting squad:", error)
      throw error
    }
  }

  /**
   * Get squads for a user
   * @param userId User ID
   * @returns Array of squads
   */
  static async getUserSquads(userId: string): Promise<Squad[]> {
    try {
      const q = query(collection(db, this.COLLECTION), where("members", "array-contains", userId))
      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Squad)
    } catch (error) {
      console.error("Error getting user squads:", error)
      throw error
    }
  }

  /**
   * Update a squad
   * @param squadId Squad ID
   * @param squadData Squad data to update
   * @returns Service response
   */
  static async updateSquad(squadId: string, squadData: SquadUpdateData): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, squadId), {
        ...squadData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating squad:", error)
      return { success: false, error }
    }
  }

  /**
   * Add a member to a squad
   * @param squadId Squad ID
   * @param userId User ID to add
   * @returns Service response
   */
  static async addMember(squadId: string, userId: string): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, squadId), {
        members: arrayUnion(userId),
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error adding member to squad:", error)
      return { success: false, error }
    }
  }

  /**
   * Remove a member from a squad
   * @param squadId Squad ID
   * @param userId User ID to remove
   * @returns Service response
   */
  static async removeMember(squadId: string, userId: string): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, squadId), {
        members: arrayRemove(userId),
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error removing member from squad:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user is the squad leader
   * @param userId User ID
   * @param squadId Squad ID
   * @returns True if the user is the squad leader
   */
  static async isUserSquadLeader(userId: string, squadId: string): Promise<boolean> {
    try {
      const squad = await this.getSquad(squadId)
      return squad?.leaderId === userId
    } catch (error) {
      console.error("Error checking if user is squad leader:", error)
      return false
    }
  }

  /**
   * Check if a user is a member of a squad
   * @param userId User ID
   * @param squadId Squad ID
   * @returns True if the user is a member of the squad
   */
  static async isUserSquadMember(userId: string, squadId: string): Promise<boolean> {
    try {
      const squad = await this.getSquad(squadId)
      return squad?.members.includes(userId) || false
    } catch (error) {
      console.error("Error checking if user is squad member:", error)
      return false
    }
  }

  /**
   * Delete a squad
   * @param squadId Squad ID
   * @returns Service response
   */
  static async deleteSquad(squadId: string): Promise<ServiceResponse> {
    try {
      // Check if squad exists
      const squad = await this.getSquad(squadId)
      if (!squad) {
        return { success: false, error: new Error("Squad not found") }
      }

      // Delete the squad
      await deleteDoc(doc(db, this.COLLECTION, squadId))

      // TODO: In a production app, we would also:
      // 1. Delete or archive all related trips
      // 2. Delete or archive all related invitations
      // 3. Possibly notify all members
      // This would typically be handled by a Cloud Function or backend service

      return { success: true }
    } catch (error) {
      console.error("Error deleting squad:", error)
      return { success: false, error }
    }
  }
}
