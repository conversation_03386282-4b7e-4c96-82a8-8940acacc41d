"use client"

import { create } from "zustand"
import { Squad } from "./squad.types"
import { SquadService } from "./squad.service"
import { useUserStore } from "../user/user.store"
import { User } from "../user/user.types"

/**
 * Squad store state interface
 */
interface SquadState {
  // State
  squads: Squad[]
  currentSquad: Squad | null
  loading: boolean
  error: Error | null

  // Selectors
  getSquadLeader: (squadId: string) => User | undefined

  // Actions
  fetchUserSquads: (userId: string) => Promise<void>
  fetchSquad: (squadId: string) => Promise<void>
  createSquad: (squadData: any) => Promise<string | null>
  updateSquad: (squadId: string, squadData: Partial<Squad>) => Promise<boolean>
  addMember: (squadId: string, userId: string) => Promise<boolean>
  removeMember: (squadId: string, userId: string) => Promise<boolean>
  clearSquads: () => void
  setCurrentSquad: (squad: Squad | null) => void
}

/**
 * Squad store with Zustand
 */
export const useSquadStore = create<SquadState>((set, get) => ({
  // Initial state
  squads: [],
  currentSquad: null,
  loading: false,
  error: null,

  // Actions
  fetchUserSquads: async (userId: string) => {
    try {
      set({ loading: true, error: null })
      const squads = await SquadService.getUserSquads(userId)
      set({ squads, loading: false })
    } catch (error) {
      console.error("Error fetching user squads:", error)
      set({ error: error as Error, loading: false })
    }
  },

  fetchSquad: async (squadId: string) => {
    try {
      set({ loading: true, error: null })
      const squad = await SquadService.getSquad(squadId)
      if (squad) {
        set({ currentSquad: squad, loading: false })
      } else {
        set({ error: new Error("Squad not found"), loading: false })
      }
    } catch (error) {
      console.error("Error fetching squad:", error)
      set({ error: error as Error, loading: false })
    }
  },

  createSquad: async (squadData) => {
    try {
      set({ loading: true, error: null })
      const squadId = await SquadService.createSquad(squadData)
      set({ loading: false })
      return squadId
    } catch (error) {
      console.error("Error creating squad:", error)
      set({ error: error as Error, loading: false })
      return null
    }
  },

  updateSquad: async (squadId, squadData) => {
    try {
      set({ loading: true, error: null })
      const result = await SquadService.updateSquad(squadId, squadData)

      // If successful and we have the current squad loaded, update it
      if (result.success && get().currentSquad?.id === squadId) {
        const updatedSquad = await SquadService.getSquad(squadId)
        if (updatedSquad) {
          set({ currentSquad: updatedSquad })
        }
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error updating squad:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  addMember: async (squadId, userId) => {
    try {
      set({ loading: true, error: null })
      const result = await SquadService.addMember(squadId, userId)

      // If successful and we have the current squad loaded, update it
      if (result.success && get().currentSquad?.id === squadId) {
        const updatedSquad = await SquadService.getSquad(squadId)
        if (updatedSquad) {
          set({ currentSquad: updatedSquad })
        }
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error adding member to squad:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  removeMember: async (squadId, userId) => {
    try {
      set({ loading: true, error: null })
      const result = await SquadService.removeMember(squadId, userId)

      // If successful and we have the current squad loaded, update it
      if (result.success && get().currentSquad?.id === squadId) {
        const updatedSquad = await SquadService.getSquad(squadId)
        if (updatedSquad) {
          set({ currentSquad: updatedSquad })
        }
      }

      set({ loading: false })
      return result.success
    } catch (error) {
      console.error("Error removing member from squad:", error)
      set({ error: error as Error, loading: false })
      return false
    }
  },

  clearSquads: () => {
    set({ squads: [], currentSquad: null })
  },

  setCurrentSquad: (squad) => {
    set({ currentSquad: squad })
  },

  // Selectors
  getSquadLeader: (squadId: string) => {
    // Find the squad by ID
    const squad = get().squads.find((s) => s.id === squadId) || get().currentSquad

    // If no squad found or no leaderId, return undefined
    if (!squad || !squad.leaderId) return undefined

    // Get the user store to access users
    const userStore = useUserStore.getState()

    // Return the user that matches the squad's leaderId
    return userStore.users[squad.leaderId]
  },
}))
