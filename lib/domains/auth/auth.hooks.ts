"use client"

import { useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import { auth } from "@/lib/firebase"
import { useAuthState } from "react-firebase-hooks/auth"
import { useAuthStore } from "./auth.store"
import { publicRoutes } from "./auth.types"

// Export real-time hooks
export * from "./auth.realtime.hooks"

// Create selector hooks for specific parts of the auth state
export const useUser = () => useAuthStore((state) => state.user)
export const useUserData = () => useAuthStore((state) => state.userData)
export const useUserDataLoading = () => useAuthStore((state) => state.userDataLoading)
export const useUpdateUserData = () => useAuthStore((state) => state.updateUserData)
export const useAuthLoading = () => useAuthStore((state) => state.loading)
export const useAuthError = () => useAuthStore((state) => state.error)
export const useIsAdmin = () => useAuthStore((state) => state.isAdmin)

/**
 * Hook to sync Firebase auth state with Zustand store
 */
export function useAuthSync() {
  const [user, loading, error] = useAuthState(auth)
  const router = useRouter()
  const pathname = usePathname()

  // Get auth store actions
  const { setUser, setLoading, setError, isProtectedRoute, refreshAdminStatus } = useAuthStore()

  // Refresh admin status when user changes
  useEffect(() => {
    if (user) {
      refreshAdminStatus()
    }
  }, [user, refreshAdminStatus])

  // Update auth store with Firebase auth state
  useEffect(() => {
    setUser(user || null)
    setLoading(loading)
    setError(error || null)
  }, [user, loading, error, setUser, setLoading, setError])

  // Handle protected routes
  useEffect(() => {
    if (!loading && !user && isProtectedRoute(pathname)) {
      router.push("/login")
    }
  }, [user, loading, pathname, router, isProtectedRoute])

  return { user, loading, error }
}

/**
 * Hook to redirect authenticated users away from auth pages
 * Use this on login, signup, forgot-password, and landing pages
 */
export function useAuthRedirect() {
  const [user, loading] = useAuthState(auth)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Only redirect if user is authenticated and we're on an auth flow page
    if (!loading && user && publicRoutes.includes(pathname)) {
      // Redirect authenticated users to dashboard
      router.push("/dashboard")
    }
  }, [user, loading, pathname, router])

  return { user, loading, isRedirecting: !loading && !!user && publicRoutes.includes(pathname) }
}

/**
 * Convenience hook for getting user data from the auth store
 */
export function useUserWithData() {
  const user = useUser()
  const userData = useUserData()
  const loading = useUserDataLoading()
  // const fetchUserData = useAuthStore((state) => state.fetchUserData)

  // // Fetch user data when user changes or when the component mounts
  useEffect(() => {}, [user])

  return { user, userData, loading }
}

/**
 * Convenience hook for auth status (user and loading)
 */
export function useAuthStatus() {
  const user = useUser()
  const loading = useAuthLoading()
  return { user, loading }
}
