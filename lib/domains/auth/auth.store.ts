"use client"

import { create } from "zustand"
import type { User as FirebaseUser } from "firebase/auth"
import { doc, getDoc, setDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { User } from "../user/user.types"
import { UserService } from "../user/user.service"
import { publicRoutes } from "./auth.types"
import { toast } from "@/components/ui/use-toast"

/**
 * Auth store state interface
 */
interface AuthState {
  // State
  user: FirebaseUser | null
  userData: User | null
  userDataLoading: boolean
  userDataError: Error | null
  userDataLastFetched: number | null
  loading: boolean
  updating: boolean
  error: Error | null
  isAdmin: boolean

  // Actions
  setUser: (user: FirebaseUser | null) => void
  setUserData: (userData: User | null) => void
  setUserDataLoading: (loading: boolean) => void
  setUserDataError: (error: Error | null) => void
  fetchUserData: () => Promise<User | null>
  updateUserData: (updates: Partial<User>, showToast?: boolean) => Promise<void>
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void
  setIsAdmin: (isAdmin: boolean) => void
  refreshAdminStatus: () => Promise<void>
  isProtectedRoute: (pathname: string) => boolean
  setUserUpdating: (updating: boolean) => void
}

/**
 * Auth store with Zustand
 */
export const useAuthStore = create<AuthState>((set, get) => ({
  // Initial state
  user: null,
  userData: null,
  userDataLoading: false,
  userDataError: null,
  userDataLastFetched: null,
  loading: true,
  updating: false,
  error: null,
  isAdmin: false,

  // Actions
  setUser: (user) => set({ user }),
  setUserData: (userData) => set({ userData }),
  setUserDataLoading: (loading) => set({ userDataLoading: loading }),
  setUserUpdating: (updating) => set({ updating }),
  setUserDataError: (error) => set({ userDataError: error }),

  fetchUserData: async () => {
    const { user, setUserDataLoading, setUserDataError, setUserData } = get()

    if (!user) {
      setUserData(null)
      return null
    }

    try {
      setUserDataLoading(true)
      setUserDataError(null)

      const userData = await UserService.getUser(user.uid)

      if (userData) {
        setUserData(userData)
        set({ userDataLastFetched: Date.now() })
      } else {
        setUserDataError(new Error("User data not found"))
      }

      setUserDataLoading(false)
      return userData
    } catch (error) {
      console.error("Error fetching user data:", error)
      setUserDataError(error as Error)
      setUserDataLoading(false)
      return null
    }
  },

  updateUserData: async (updates) => {
    const { user, userData, setUserDataLoading, setUserDataError } = get()

    if (!user || !userData) {
      return
    }

    try {
      setUserDataLoading(true)
      setUserDataError(null)

      await UserService.updateUser(user.uid, updates)

      // Refresh user data
      await get().fetchUserData()
      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully.",
      })
      setUserDataLoading(false)
    } catch (error) {
      console.error("Error updating user data:", error)
      setUserDataError(error as Error)
      setUserDataLoading(false)
    }
  },

  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  setIsAdmin: (isAdmin) => set({ isAdmin }),

  refreshAdminStatus: async () => {
    const { user } = get()

    if (!user) {
      set({ isAdmin: false })
      return
    }

    try {
      // Check if user document exists
      const userRef = doc(db, "users", user.uid)
      const userSnap = await getDoc(userRef)

      if (userSnap.exists()) {
        // User document exists, get the admin status
        const data = userSnap.data()
        set({ isAdmin: data.isAdmin === true })
      } else {
        // User document doesn't exist, create a basic one
        const newUserData = {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL,
          createdAt: serverTimestamp(),
          isAdmin: false,
        }

        try {
          await setDoc(userRef, newUserData)
          set({ isAdmin: false })
        } catch (err) {
          console.error("Error creating user document:", err)
          set({ isAdmin: false })
        }
      }
    } catch (err) {
      console.error("Error refreshing admin status:", err)
      set({ error: err as Error })
    }
  },

  isProtectedRoute: (pathname) => {
    return !publicRoutes.includes(pathname)
  },
}))
