import { auth } from "@/lib/firebase"
import { onAuthStateChanged, User as FirebaseUser } from "firebase/auth"
import { BaseRealtimeService } from "../base/base.realtime.service"

/**
 * Auth real-time service for Firebase real-time operations
 */
export class AuthRealtimeService {
  /**
   * Subscribe to auth state changes
   * @param callback Callback function to handle auth state changes
   * @returns Unsubscribe function
   */
  static subscribeToAuthState(
    callback: (user: FirebaseUser | null, error?: Error) => void
  ): () => void {
    try {
      return onAuthStateChanged(
        auth,
        (user) => {
          callback(user)
        },
        (error) => {
          console.error("Error in auth state subscription:", error)
          callback(null, error)
        }
      )
    } catch (error) {
      console.error("Error setting up auth state subscription:", error)
      callback(null, error as Error)
      // Return a no-op unsubscribe function
      return () => {}
    }
  }
}
