import { auth } from "@/lib/firebase"
import { signOut, sendPasswordResetEmail } from "firebase/auth"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"

/**
 * Auth service for Firebase authentication operations
 */
export class AuthService {
  /**
   * Log out the current user
   * @returns Service response indicating success or failure
   */
  static async logOut(): Promise<ServiceResponse> {
    try {
      await signOut(auth)
      return { success: true }
    } catch (error) {
      console.error("Error logging out:", error)
      return { success: false, error }
    }
  }

  /**
   * Get the current authentication token
   * @returns The authentication token or null if not authenticated
   */
  static async getAuthToken(): Promise<string | null> {
    try {
      const currentUser = auth.currentUser
      if (!currentUser) {
        return null
      }

      const token = await currentUser.getIdToken()
      return token
    } catch (error) {
      console.error("Error getting auth token:", error)
      return null
    }
  }

  /**
   * Send a password reset email to the specified email address
   * @param email The email address to send the password reset email to
   * @returns Service response indicating success or failure
   */
  static async sendPasswordResetEmail(email: string): Promise<ServiceResponse> {
    try {
      await sendPasswordResetEmail(auth, email)
      return { success: true }
    } catch (error: any) {
      console.error("Error sending password reset email:", error)

      // Handle specific Firebase auth errors
      let errorMessage = "Failed to send password reset email"

      switch (error.code) {
        case "auth/user-not-found":
          errorMessage = "No account found with this email address"
          break
        case "auth/invalid-email":
          errorMessage = "Invalid email address"
          break
        case "auth/too-many-requests":
          errorMessage = "Too many requests. Please try again later"
          break
        default:
          errorMessage = error.message || "Failed to send password reset email"
      }

      return { success: false, error: new Error(errorMessage) }
    }
  }
}
