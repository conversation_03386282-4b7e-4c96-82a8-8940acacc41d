import { User as FirebaseUser } from "firebase/auth"
import { Timestamp } from "firebase/firestore"
import { BaseEntity } from "../base/base.types"

/**
 * Admin status interface
 */
export interface AdminStatus {
  isAdmin: boolean
}

/**
 * Auth user interface
 */
export interface AuthUser {
  user: FirebaseUser | null
  isAdmin: boolean
  loading: boolean
  error: Error | null
}

/**
 * Public routes that don't require authentication
 */
export const publicRoutes = ["/", "/login", "/signup", "/forgot-password", "/terms", "/privacy"]
