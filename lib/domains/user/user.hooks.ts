"use client"

import { useEffect, useState } from "react"
import { useUserStore } from "./user.store"
import { useUser } from "../auth/auth.hooks"
import { User, UserUpdateData } from "./user.types"
import { UserService } from "./user.service"

// Export real-time hooks
export * from "./user.realtime.hooks"

/**
 * Hook to get a user by ID
 */
export const useUserById = (userId: string, useRealtime: boolean = false) => {
  const { users, loading, error, fetchUser, subscribeToUser, unsubscribeFromUser } = useUserStore()

  useEffect(() => {
    if (userId) {
      if (useRealtime) {
        // Use real-time subscription
        subscribeToUser(userId)

        // Cleanup on unmount
        return () => {
          unsubscribeFromUser()
        }
      } else {
        // Use regular fetch
        fetchUser(userId)
      }
    }

    // Return empty cleanup function for TypeScript
    return () => {}
  }, [userId, fetchUser, subscribeToUser, unsubscribeFromUser, useRealtime])

  return { user: users[userId] || null, loading, error }
}

/**
 * Hook to get the current authenticated user's data
 */
export const useCurrentUser = (useRealtime: boolean = false) => {
  const authUser = useUser()
  const userId = authUser?.uid

  return useUserById(userId || "", useRealtime)
}

/**
 * Hook to get multiple users by their IDs
 * @param userIds Array of user IDs to fetch
 * @returns Object containing users array, loading state, and error
 */
export const useUsersByIds = (userIds: string[]) => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchUsers = async () => {
      // Reset states
      setLoading(true)
      setError(null)

      // Handle empty array case
      if (!userIds || userIds.length === 0) {
        setUsers([])
        setLoading(false)
        return
      }

      try {
        // Fetch users using the UserService
        const fetchedUsers = await UserService.getUsersFromIds(userIds)
        setUsers(fetchedUsers)
      } catch (err) {
        console.error("Error fetching users:", err)
        setError(err as Error)
      } finally {
        setLoading(false)
      }
    }

    fetchUsers()
  }, [userIds])

  return { users, loading, error }
}

export const useUpdateUser = () => {
  const { updateUser: updateUserOnStore } = useUserStore()
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const updateUser = async (userId: string, userData: UserUpdateData) => {
    try {
      setUpdating(true)
      setError(null)
      const success = await updateUserOnStore(userId, userData)
      setUpdating(false)
      return success
    } catch (err) {
      setError(err as Error)
      setUpdating(false)
      return false
    }
  }
  return { updateUser, updating, error }
}
