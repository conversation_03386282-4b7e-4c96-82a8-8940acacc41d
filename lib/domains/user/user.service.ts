import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import { User, UserUpdateData } from "./user.types"

/**
 * User service for Firebase operations
 */
export class UserService {
  private static readonly COLLECTION = "users"

  /**
   * Get a user by ID
   * @param userId User ID
   * @returns The user data or null if not found
   */
  static async getUser(userId: string): Promise<User | null> {
    try {
      const userDoc = await getDoc(doc(db, this.COLLECTION, userId))
      if (userDoc.exists()) {
        return { ...userDoc.data(), uid: userId } as User
      }
      return null
    } catch (error) {
      console.error("Error getting user:", error)
      throw error
    }
  }

  /**
   * Get multiple users by their IDs
   * @param userIds Array of user IDs to fetch
   * @returns Array of user data
   */
  static async getUsersFromIds(userIds: string[]): Promise<User[]> {
    try {
      // Ensure userIds is an array
      if (!Array.isArray(userIds)) {
        console.error("userIds is not an array:", userIds)
        return []
      }

      const users: User[] = []
      for (const userId of userIds) {
        const user = await this.getUser(userId)
        if (user) {
          users.push(user)
        }
      }
      return users
    } catch (error) {
      console.error("Error getting users from IDs:", error)
      throw error
    }
  }

  /**
   * Get a user by email
   * @param email User email
   * @returns The user data or null if not found
   */
  static async getUserByEmail(email: string): Promise<User | null> {
    try {
      const usersRef = collection(db, this.COLLECTION)
      const q = query(usersRef, where("email", "==", email))
      const querySnapshot = await getDocs(q)

      if (!querySnapshot.empty) {
        const userDoc = querySnapshot.docs[0]
        return { ...userDoc.data(), uid: userDoc.id } as User
      }

      return null
    } catch (error) {
      console.error("Error getting user by email:", error)
      throw error
    }
  }

  /**
   * Update a user
   * @param userId User ID
   * @param userData User data to update
   * @returns Service response indicating success or failure
   */
  static async updateUser(userId: string, userData: UserUpdateData): Promise<ServiceResponse> {
    try {
      const userRef = doc(db, this.COLLECTION, userId)

      await updateDoc(userRef, {
        ...userData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating user:", error)
      return { success: false, error }
    }
  }

  /**
   * Ensure a user document exists
   * @param user Firebase user object
   * @returns The user data or null if creation failed
   */
  static async ensureUserDocument(user: any): Promise<User | null> {
    if (!user) return null

    try {
      const userDoc = await getDoc(doc(db, this.COLLECTION, user.uid))
      if (!userDoc.exists()) {
        // Create user document if it doesn't exist
        await setDoc(doc(db, this.COLLECTION, user.uid), {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName || "",
          photoURL: user.photoURL || "",
          createdAt: serverTimestamp(),
          subscriptionPlan: "free",
          subscriptionStatus: null,
        })
      }
      return userDoc.exists() ? ({ ...userDoc.data(), uid: user.uid } as User) : null
    } catch (error) {
      console.error("Error ensuring user document:", error)
      return null
    }
  }
}
