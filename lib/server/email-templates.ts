/**
 * Email template configuration
 *
 * This file contains the template IDs for emails sent via Brevo.
 * Update these IDs with your actual template IDs from the Brevo platform.
 */

export const EmailTemplates = {
  // Template for squad invitations
  INVITATION: process.env.BREVO_INVITATION_TEMPLATE_ID
    ? Number(process.env.BREVO_INVITATION_TEMPLATE_ID)
    : undefined,

  // Template for welcome emails
  WELCOME: process.env.BREVO_WELCOME_TEMPLATE_ID
    ? Number(process.env.BREVO_WELCOME_TEMPLATE_ID)
    : undefined,

  // Template for password reset
  PASSWORD_RESET: process.env.BREVO_PASSWORD_RESET_TEMPLATE_ID
    ? Number(process.env.BREVO_PASSWORD_RESET_TEMPLATE_ID)
    : undefined,
}
