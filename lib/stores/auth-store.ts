"use client"

import { create } from "zustand"
import type { User as FirebaseUser } from "firebase/auth"
import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { getUser, type User as UserData } from "@/lib/firebase/user-service"

// Define the admin status type
export interface AdminStatus {
  isAdmin: boolean
}

// Public routes that don't require authentication
export const publicRoutes = ["/", "/login", "/signup", "/terms", "/privacy"]

// Define the auth store state
interface AuthState {
  // State
  user: FirebaseUser | null
  userData: UserData | null
  userDataLoading: boolean
  userDataError: Error | null
  userDataLastFetched: number | null
  loading: boolean
  error: Error | null
  isAdmin: boolean

  // Actions
  setUser: (user: FirebaseUser | null) => void
  setUserData: (userData: UserData | null) => void
  setUserDataLoading: (loading: boolean) => void
  setUserDataError: (error: Error | null) => void
  fetchUserData: () => Promise<UserData | null>
  updateUserData: (updates: Partial<UserData>, showToast?: boolean) => Promise<void>
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void
  setIsAdmin: (isAdmin: boolean) => void
  refreshAdminStatus: () => Promise<void>
  isProtectedRoute: (pathname: string) => boolean
}

// Create the auth store
export const useAuthStore = create<AuthState>((set, get) => ({
  // Initial state
  user: null,
  userData: null,
  userDataLoading: false,
  userDataError: null,
  userDataLastFetched: null,
  loading: true,
  error: null,
  isAdmin: false,

  // Actions
  setUser: (user) => set({ user }),
  setUserData: (userData) => set({ userData }),
  setUserDataLoading: (loading) => set({ userDataLoading: loading }),
  setUserDataError: (error) => set({ userDataError: error }),
  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  setIsAdmin: (isAdmin) => set({ isAdmin }),

  // Fetch user data with caching
  fetchUserData: async () => {
    const { user, userDataLastFetched } = get()

    // If no user, clear the data
    if (!user) {
      set({ userData: null, userDataLoading: false, userDataError: null })
      return null
    }

    // Check if we've already fetched data recently (within the last 5 minutes)
    const now = Date.now()
    const fiveMinutes = 5 * 60 * 1000
    if (userDataLastFetched && now - userDataLastFetched < fiveMinutes && get().userData) {
      // Data is fresh enough, no need to fetch again
      return get().userData
    }

    try {
      set({ userDataLoading: true })
      const data = await getUser(user.uid)
      set({ userData: data, userDataLoading: false, userDataLastFetched: now })
      return data
    } catch (error) {
      console.error("Error fetching user data:", error)
      set({ userDataError: error as Error, userDataLoading: false })
      return null
    }
  },

  // Update user data in Firestore and in the store
  updateUserData: async (updates, showToast = false) => {
    const { user, userData } = get()
    if (!user || !userData) return

    try {
      await updateDoc(doc(db, "users", user.uid), updates)
      // Update the local store
      set({ userData: { ...userData, ...updates } })

      // Show toast notification if requested
      if (showToast) {
        // Import toast dynamically to avoid circular dependencies
        const { toast } = await import("@/components/ui/use-toast")
        toast({
          title: "Profile updated",
          description: "Your profile has been updated successfully.",
        })
      }
    } catch (error) {
      console.error("Error updating user data:", error)

      // Show error toast if requested
      if (showToast) {
        // Import toast dynamically to avoid circular dependencies
        const { toast } = await import("@/components/ui/use-toast")
        toast({
          title: "Error",
          description: "Failed to update profile. Please try again.",
          variant: "destructive",
        })
      }

      throw error
    }
  },

  // Check if a route requires authentication
  isProtectedRoute: (pathname: string) => {
    if (!pathname) return false

    // API routes and invitation routes have special handling
    if (pathname.startsWith("/api/")) return false
    if (pathname.startsWith("/invitation/")) return false

    // Check if the route is in the public routes list
    return !publicRoutes.includes(pathname)
  },

  // Function to refresh admin status only
  refreshAdminStatus: async () => {
    const { user } = get()
    if (!user) return

    try {
      // Check if user document exists
      const userRef = doc(db, "users", user.uid)
      const userSnap = await getDoc(userRef)

      if (userSnap.exists()) {
        // User document exists, get the admin status
        const data = userSnap.data()
        set({ isAdmin: data.isAdmin === true })
      } else {
        // User document doesn't exist, create a basic one
        const newUserData = {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL,
          createdAt: serverTimestamp(),
          isAdmin: false,
        }

        try {
          await setDoc(userRef, newUserData)
          set({ isAdmin: false })
        } catch (err) {
          console.error("Error creating user document:", err)
          set({ isAdmin: false })
        }
      }
    } catch (err) {
      console.error("Error refreshing admin status:", err)
      set({ error: err as Error })
    }
  },
}))
