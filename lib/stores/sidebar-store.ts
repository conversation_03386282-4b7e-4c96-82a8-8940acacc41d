"use client"

import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"

// Define the sidebar store state
interface SidebarState {
  // State
  isOpen: boolean
  isMobileOpen: boolean

  // Actions
  setIsOpen: (isOpen: boolean) => void
  setMobileOpen: (isOpen: boolean) => void
  toggleSidebar: () => void
  toggleMobileSidebar: () => void
}

// Create a custom storage object that only uses localStorage on the client side
const customStorage = {
  getItem: (name: string) => {
    if (typeof window === "undefined") return null
    return window.localStorage.getItem(name)
  },
  setItem: (name: string, value: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(name, value)
    }
  },
  removeItem: (name: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(name)
    }
  },
}

// Create the sidebar store
export const useSidebarStore = create<SidebarState>()(
  persist(
    (set) => ({
      // Initial state
      isOpen: false,
      isMobileOpen: false,

      // Actions
      setIsOpen: (isOpen) => set({ isOpen }),
      setMobileOpen: (isOpen) => set({ isMobileOpen: isOpen }),
      toggleSidebar: () => set((state) => ({ isOpen: !state.isOpen })),
      toggleMobileSidebar: () => set((state) => ({ isMobileOpen: !state.isMobileOpen })),
    }),
    {
      name: "brotrips-sidebar-storage",
      storage: createJSONStorage(() => customStorage),
      skipHydration: true, // Skip hydration to prevent hydration mismatch
    }
  )
)
