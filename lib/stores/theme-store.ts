"use client"

import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
import { getUserPreferences, updateUserTheme } from "@/lib/firebase/user-preferences-service"

// Local storage key for theme
export const THEME_STORAGE_KEY = "brotrips-theme"

// Define the theme store state
interface ThemeState {
  // Theme state
  theme: "light" | "dark" | "system"
  isLoading: boolean
  isInitialized: boolean

  // Actions
  setTheme: (theme: "light" | "dark" | "system", userId?: string) => Promise<void>
  initializeTheme: (userId?: string) => Promise<void>
}

// Create a custom storage object that only uses localStorage on the client side
const customStorage = {
  getItem: (name: string) => {
    if (typeof window === "undefined") return null
    return window.localStorage.getItem(name)
  },
  setItem: (name: string, value: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.setItem(name, value)
    }
  },
  removeItem: (name: string) => {
    if (typeof window !== "undefined") {
      window.localStorage.removeItem(name)
    }
  },
}

// Helper functions for localStorage
export const getLocalStorageTheme = (): "light" | "dark" | "system" | null => {
  if (typeof window === "undefined") return null
  const theme = window.localStorage.getItem(THEME_STORAGE_KEY)
  return (theme as "light" | "dark" | "system") || null
}

export const setLocalStorageTheme = (theme: "light" | "dark" | "system") => {
  if (typeof window === "undefined") return
  window.localStorage.setItem(THEME_STORAGE_KEY, theme)
}

// Create the theme store
export const useThemeStore = create<ThemeState>()(
  persist(
    (set) => ({
      theme: "system",
      isLoading: false,
      isInitialized: false,

      setTheme: async (theme, userId) => {
        // Update the store state
        set({ theme })

        // Save to localStorage for immediate access
        setLocalStorageTheme(theme)

        // If userId is provided, update in the database
        if (userId) {
          try {
            await updateUserTheme(userId, theme)
          } catch (error) {
            console.error("Error updating theme in database:", error)
            // Don't throw here - we've already updated localStorage and the store
          }
        }
      },

      initializeTheme: async (userId) => {
        // Set loading state
        set({ isLoading: true })

        try {
          // First check localStorage for immediate theme application
          const localStorageTheme = getLocalStorageTheme()

          if (localStorageTheme) {
            // If we have a theme in localStorage, use it immediately
            set({ theme: localStorageTheme })
          }

          // If user is logged in, check for theme in database
          if (userId) {
            const userPreferences = await getUserPreferences(userId)

            if (userPreferences?.theme) {
              // If localStorage and database themes differ, prefer localStorage
              // but update the database in the background
              if (localStorageTheme && localStorageTheme !== userPreferences.theme) {
                updateUserTheme(userId, localStorageTheme).catch((error) => {
                  console.error("Error syncing localStorage theme to database:", error)
                })
              } else if (!localStorageTheme) {
                // If no localStorage theme but we have a database preference, use it
                set({ theme: userPreferences.theme })
                setLocalStorageTheme(userPreferences.theme)
              }
            }
          }
        } catch (error) {
          console.error("Error initializing theme:", error)
        } finally {
          // Mark as initialized and not loading
          set({ isLoading: false, isInitialized: true })
        }
      },
    }),
    {
      name: "brotrips-theme-storage",
      storage: createJSONStorage(() => customStorage),
      skipHydration: true, // Skip hydration to prevent hydration mismatch
      partialize: (state) => ({
        // Only persist the theme, not the loading states
        theme: state.theme,
      }),
    }
  )
)
