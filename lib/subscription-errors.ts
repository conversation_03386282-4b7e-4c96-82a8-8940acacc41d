import { SUBSCRIPTION_LIMITS } from "./stripe-client"
import { toast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { AI_USAGE_LIMITS } from "./firebase/ai-usage-service"

/**
 * Subscription Error Types
 */
export enum SubscriptionErrorType {
  MAX_SQUADS_REACHED = "MAX_SQUADS_REACHED",
  MAX_TRIPS_PER_SQUAD_REACHED = "MAX_TRIPS_PER_SQUAD_REACHED",
  DAILY_AI_LIMIT_REACHED = "DAILY_AI_LIMIT_REACHED",
  WEEKLY_AI_LIMIT_REACHED = "WEEKLY_AI_LIMIT_REACHED",
  TRIP_AI_LIMIT_REACHED = "TRIP_AI_LIMIT_REACHED",
  TASK_AI_LIMIT_REACHED = "TASK_AI_LIMIT_REACHED",
  ITINERARY_AI_LIMIT_REACHED = "ITINERARY_AI_LIMIT_REACHED",
  GENERIC_ERROR = "GENERIC_ERROR",
}

/**
 * Subscription Error Messages
 */
export const SUBSCRIPTION_ERROR_MESSAGES = {
  [SubscriptionErrorType.MAX_SQUADS_REACHED]: {
    title: "Subscription limit reached",
    description: `You've reached the maximum number of squads (${SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS}) for the free plan. Upgrade to create more.`,
  },
  [SubscriptionErrorType.MAX_TRIPS_PER_SQUAD_REACHED]: {
    title: "Subscription limit reached",
    description: `You've reached the maximum number of trips (${SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD}) for this squad on the free plan. Upgrade to create more.`,
  },
  [SubscriptionErrorType.DAILY_AI_LIMIT_REACHED]: {
    title: "AI usage limit reached",
    description: `You've reached your limit of AI suggestions. Upgrade to Pro for unlimited suggestions.`,
  },
  [SubscriptionErrorType.WEEKLY_AI_LIMIT_REACHED]: {
    title: "AI usage limit reached",
    description: `You've reached your limit of AI suggestions. Upgrade to Pro for unlimited suggestions.`,
  },
  [SubscriptionErrorType.TRIP_AI_LIMIT_REACHED]: {
    title: "Trip suggestions limit reached",
    description: `You've reached your limit of trip suggestions. Upgrade to Pro for unlimited suggestions.`,
  },
  [SubscriptionErrorType.TASK_AI_LIMIT_REACHED]: {
    title: "Task suggestions limit reached",
    description: `You've reached your limit of task suggestions. Upgrade to Pro for unlimited suggestions.`,
  },
  [SubscriptionErrorType.ITINERARY_AI_LIMIT_REACHED]: {
    title: "Activity suggestions limit reached",
    description: `You've reached your limit of activity suggestions. Upgrade to Pro for unlimited suggestions.`,
  },
  [SubscriptionErrorType.GENERIC_ERROR]: {
    title: "Subscription error",
    description: "There was an error with your subscription. Please try again or contact support.",
  },
}

/**
 * Shows a subscription error toast and redirects to the billing page
 */
export const handleSubscriptionError = (
  errorType: SubscriptionErrorType,
  router?: ReturnType<typeof useRouter>
) => {
  const errorMessage = SUBSCRIPTION_ERROR_MESSAGES[errorType]

  // Show toast with error message
  toast({
    title: errorMessage.title,
    description: errorMessage.description,
    variant: "destructive",
  })

  // Redirect to billing page if router is provided
  if (router) {
    router.push("/settings?tab=billing")
  }

  return false
}

/**
 * Creates an alert message for subscription errors
 */
export const getSubscriptionErrorAlert = (errorType: SubscriptionErrorType) => {
  const errorMessage = SUBSCRIPTION_ERROR_MESSAGES[errorType]

  return {
    title: errorMessage.title,
    description: errorMessage.description,
  }
}
