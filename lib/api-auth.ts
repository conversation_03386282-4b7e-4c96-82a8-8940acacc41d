import { NextRequest, NextResponse } from "next/server"
import { verifyAuthToken } from "./firebase-admin"

// Helper function to verify authentication for API routes
export async function verifyAuth(req: NextRequest) {
  try {
    // Get the token from the X-Auth-Token header (set by middleware)
    const token = req.headers.get("X-Auth-Token")

    if (!token) {
      return {
        isAuthenticated: false,
        response: NextResponse.json({ error: "Unauthorized: Missing token" }, { status: 401 }),
        userId: null,
      }
    }

    // Verify the token with Firebase Admin
    const authResult = await verifyAuthToken(token)

    if (!authResult.isValid) {
      return {
        isAuthenticated: false,
        response: NextResponse.json({ error: "Unauthorized: Invalid token" }, { status: 401 }),
        userId: null,
      }
    }

    // Authentication successful
    return {
      isAuthenticated: true,
      response: null,
      userId: authResult.uid || null,
    }
  } catch (error) {
    console.error("Error verifying authentication:", error)
    return {
      isAuthenticated: false,
      response: NextResponse.json({ error: "Authentication error" }, { status: 500 }),
      userId: null,
    }
  }
}
